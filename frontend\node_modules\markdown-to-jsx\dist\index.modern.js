import*as e from"react";function t(){return t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},t.apply(this,arguments)}const n=["children","options"],r={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"};var i;!function(e){e[e.MAX=0]="MAX",e[e.HIGH=1]="HIGH",e[e.MED=2]="MED",e[e.LOW=3]="LOW",e[e.MIN=4]="MIN"}(i||(i={}));const l=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce((e,t)=>(e[t.toLowerCase()]=t,e),{class:"className",for:"htmlFor"}),o={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},a=["style","script"],c=["src","href","data","formAction","srcDoc","action"],s=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,d=/mailto:/i,u=/\n{2,}$/,p=/^(\s*>[\s\S]*?)(?=\n\n|$)/,f=/^ *> ?/gm,h=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,m=/^ {2,}\n/,g=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,y=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,k=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,x=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,b=/^(?:\n *)*\n/,v=/\r\n?/g,C=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,$=/^\[\^([^\]]+)]/,S=/\f/g,w=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,E=/^\s*?\[(x|\s)\]/,z=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,L=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,A=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,O=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,T=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,B=/^<!--[\s\S]*?(?:-->)/,M=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,R=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,I=/^\{.*\}$/,D=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,U=/^<([^ >]+@[^ >]+)>/,N=/^<([^ >]+:\/[^ >]+)>/,j=/-([a-z])?/gi,H=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,P=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,_=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,F=/^\[([^\]]*)\] ?\[([^\]]*)\]/,W=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,G=/\t/g,Z=/(^ *\||\| *$)/g,q=/^ *:-+: *$/,Q=/^ *:-+ *$/,V=/^ *-+: *$/,X="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",J=new RegExp(`^([*_])\\1${X}\\1\\1(?!\\1)`),K=new RegExp(`^([*_])${X}\\1(?!\\1)`),Y=new RegExp(`^(==)${X}\\1`),ee=new RegExp(`^(~~)${X}\\1`),te=/^\\([^0-9A-Za-z\s])/,ne=/\\([^0-9A-Za-z\s])/g,re=/^([\s\S](?:(?!  |[0-9]\.)[^=*_~\-\n<`\\\[!])*)/,ie=/^\n+/,le=/^([ \t]*)/,oe=/\\([^\\])/g,ae=/(?:^|\n)( *)$/,ce="(?:\\d+\\.)",se="(?:[*+-])";function de(e){return"( *)("+(1===e?ce:se)+") +"}const ue=de(1),pe=de(2);function fe(e){return new RegExp("^"+(1===e?ue:pe))}const he=fe(1),me=fe(2);function ge(e){return new RegExp("^"+(1===e?ue:pe)+"[^\\n]*(?:\\n(?!\\1"+(1===e?ce:se)+" )[^\\n]*)*(\\n|$)","gm")}const ye=ge(1),ke=ge(2);function xe(e){const t=1===e?ce:se;return new RegExp("^( *)("+t+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+t+" (?!"+t+" ))\\n*|\\s*\\n*$)")}const be=xe(1),ve=xe(2);function Ce(e,t){const n=1===t,i=n?be:ve,l=n?ye:ke,o=n?he:me;return{match:Me(function(e,t){const n=ae.exec(t.prevCapture);return n&&(t.list||!t.inline&&!t.simple)?i.exec(e=n[1]+e):null}),order:1,parse(e,t,r){const i=n?+e[2]:void 0,a=e[0].replace(u,"\n").match(l);let c=!1;return{items:a.map(function(e,n){const i=o.exec(e)[0].length,l=new RegExp("^ {1,"+i+"}","gm"),s=e.replace(l,"").replace(o,""),d=n===a.length-1,u=-1!==s.indexOf("\n\n")||d&&c;c=u;const p=r.inline,f=r.list;let h;r.list=!0,u?(r.inline=!1,h=ze(s)+"\n\n"):(r.inline=!0,h=ze(s));const m=t(h,r);return r.inline=p,r.list=f,m}),ordered:n,start:i}},render:(t,n,i)=>e(t.ordered?"ol":"ul",{key:i.key,start:t.type===r.orderedList?t.start:void 0},t.items.map(function(t,r){return e("li",{key:r},n(t,i))}))}}const $e=new RegExp("^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['\"]([\\s\\S]*?)['\"])?\\s*\\)"),Se=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,we=[p,y,k,z,A,L,H,be,ve],Ee=[...we,/^[^\n]+(?:  \n|\n{2,})/,O,B,R];function ze(e){let t=e.length;for(;t>0&&e[t-1]<=" ";)t--;return e.slice(0,t)}function Le(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Ae(e){return V.test(e)?"right":q.test(e)?"center":Q.test(e)?"left":null}function Oe(e,t,n,r){const i=n.inTable;n.inTable=!0;let l=[[]],o="";function a(){if(!o)return;const e=l[l.length-1];e.push.apply(e,t(o,n)),o=""}return e.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach((e,t,n)=>{"|"===e.trim()&&(a(),r)?0!==t&&t!==n.length-1&&l.push([]):o+=e}),a(),n.inTable=i,l}function Te(e,t,n){n.inline=!0;const i=e[2]?e[2].replace(Z,"").split("|").map(Ae):[],l=e[3]?function(e,t,n){return e.trim().split("\n").map(function(e){return Oe(e,t,n,!0)})}(e[3],t,n):[],o=Oe(e[1],t,n,!!l.length);return n.inline=!1,l.length?{align:i,cells:l,header:o,type:r.table}:{children:o,type:r.paragraph}}function Be(e,t){return null==e.align[t]?{}:{textAlign:e.align[t]}}function Me(e){return e.inline=1,e}function Re(e){return Me(function(t,n){return n.inline?e.exec(t):null})}function Ie(e){return Me(function(t,n){return n.inline||n.simple?e.exec(t):null})}function De(e){return function(t,n){return n.inline||n.simple?null:e.exec(t)}}function Ue(e){return Me(function(t){return e.exec(t)})}function Ne(e,t){if(t.inline||t.simple)return null;let n="";e.split("\n").every(e=>(e+="\n",!we.some(t=>t.test(e))&&(n+=e,!!e.trim())));const r=ze(n);return""==r?null:[n,,r]}const je=/(javascript|vbscript|data(?!:image)):/i;function He(e){try{const t=decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"");if(je.test(t))return null}catch(e){return null}return e}function Pe(e){return e.replace(oe,"$1")}function _e(e,t,n){const r=n.inline||!1,i=n.simple||!1;n.inline=!0,n.simple=!0;const l=e(t,n);return n.inline=r,n.simple=i,l}function Fe(e,t,n){const r=n.inline||!1,i=n.simple||!1;n.inline=!1,n.simple=!0;const l=e(t,n);return n.inline=r,n.simple=i,l}function We(e,t,n){const r=n.inline||!1;n.inline=!1;const i=e(t,n);return n.inline=r,i}const Ge=(e,t,n)=>({children:_e(t,e[2],n)});function Ze(){return{}}function qe(){return null}function Qe(...e){return e.filter(Boolean).join(" ")}function Ve(e,t,n){let r=e;const i=t.split(".");for(;i.length&&(r=r[i[0]],void 0!==r);)i.shift();return r||n}function Xe(n="",i={}){function u(e,n,...r){const l=Ve(i.overrides,`${e}.props`,{});return i.createElement(function(e,t){const n=Ve(t,e);return n?"function"==typeof n||"object"==typeof n&&"render"in n?n:Ve(t,`${e}.component`,e):e}(e,i.overrides),t({},n,l,{className:Qe(null==n?void 0:n.className,l.className)||void 0}),...r)}function Z(e){e=e.replace(w,"");let t=!1;i.forceInline?t=!0:i.forceBlock||(t=!1===W.test(e));const n=ae(oe(t?e:`${ze(e).replace(ie,"")}\n\n`,{inline:t}));for(;"string"==typeof n[n.length-1]&&!n[n.length-1].trim();)n.pop();if(null===i.wrapper)return n;const r=i.wrapper||(t?"span":"div");let l;if(n.length>1||i.forceWrapper)l=n;else{if(1===n.length)return l=n[0],"string"==typeof l?u("span",{key:"outer"},l):l;l=null}return i.createElement(r,{key:"outer"},l)}function q(e,t){const n=t.match(s);return n?n.reduce(function(t,n){const r=n.indexOf("=");if(-1!==r){const o=function(e){return-1!==e.indexOf("-")&&null===e.match(M)&&(e=e.replace(j,function(e,t){return t.toUpperCase()})),e}(n.slice(0,r)).trim(),a=function(e){const t=e[0];return('"'===t||"'"===t)&&e.length>=2&&e[e.length-1]===t?e.slice(1,-1):e}(n.slice(r+1).trim()),s=l[o]||o;if("ref"===s)return t;const d=t[s]=function(e,t,n,r){return"style"===t?function(e){const t=[];let n="",r=!1,i=!1,l="";if(!e)return t;for(let o=0;o<e.length;o++){const a=e[o];if('"'!==a&&"'"!==a||r||(i?a===l&&(i=!1,l=""):(i=!0,l=a)),"("===a&&n.endsWith("url")?r=!0:")"===a&&r&&(r=!1),";"!==a||i||r)n+=a;else{const e=n.trim();if(e){const n=e.indexOf(":");if(n>0){const r=e.slice(0,n).trim(),i=e.slice(n+1).trim();t.push([r,i])}}n=""}}const o=n.trim();if(o){const e=o.indexOf(":");if(e>0){const n=o.slice(0,e).trim(),r=o.slice(e+1).trim();t.push([n,r])}}return t}(n).reduce(function(t,[n,i]){return t[n.replace(/(-[a-z])/g,e=>e[1].toUpperCase())]=r(i,e,n),t},{}):-1!==c.indexOf(t)?r(n,e,t):(n.match(I)&&(n=n.slice(1,n.length-1)),"true"===n||"false"!==n&&n)}(e,o,a,i.sanitizer);"string"==typeof d&&(O.test(d)||R.test(d))&&(t[s]=Z(d.trim()))}else"style"!==n&&(t[l[n]||n]=!0);return t},{}):null}i.overrides=i.overrides||{},i.sanitizer=i.sanitizer||He,i.slugify=i.slugify||Le,i.namedCodesToUnicode=i.namedCodesToUnicode?t({},o,i.namedCodesToUnicode):o,i.createElement=i.createElement||e.createElement;const Q=[],V={},X={[r.blockQuote]:{match:De(p),order:1,parse(e,t,n){const[,r,i]=e[0].replace(f,"").match(h);return{alert:r,children:t(i,n)}},render(e,t,n){const l={key:n.key};return e.alert&&(l.className="markdown-alert-"+i.slugify(e.alert.toLowerCase(),Le),e.children.unshift({attrs:{},children:[{type:r.text,text:e.alert}],noInnerParse:!0,type:r.htmlBlock,tag:"header"})),u("blockquote",l,t(e.children,n))}},[r.breakLine]:{match:Ue(m),order:1,parse:Ze,render:(e,t,n)=>u("br",{key:n.key})},[r.breakThematic]:{match:De(g),order:1,parse:Ze,render:(e,t,n)=>u("hr",{key:n.key})},[r.codeBlock]:{match:De(k),order:0,parse:e=>({lang:void 0,text:ze(e[0].replace(/^ {4}/gm,"")).replace(ne,"$1")}),render:(e,n,r)=>u("pre",{key:r.key},u("code",t({},e.attrs,{className:e.lang?`lang-${e.lang}`:""}),e.text))},[r.codeFenced]:{match:De(y),order:0,parse:e=>({attrs:q("code",e[3]||""),lang:e[2]||void 0,text:e[4],type:r.codeBlock})},[r.codeInline]:{match:Ie(x),order:3,parse:e=>({text:e[2].replace(ne,"$1")}),render:(e,t,n)=>u("code",{key:n.key},e.text)},[r.footnote]:{match:De(C),order:0,parse:e=>(Q.push({footnote:e[2],identifier:e[1]}),{}),render:qe},[r.footnoteReference]:{match:Re($),order:1,parse:e=>({target:`#${i.slugify(e[1],Le)}`,text:e[1]}),render:(e,t,n)=>u("a",{key:n.key,href:i.sanitizer(e.target,"a","href")},u("sup",{key:n.key},e.text))},[r.gfmTask]:{match:Re(E),order:1,parse:e=>({completed:"x"===e[1].toLowerCase()}),render:(e,t,n)=>u("input",{checked:e.completed,key:n.key,readOnly:!0,type:"checkbox"})},[r.heading]:{match:De(i.enforceAtxHeadings?L:z),order:1,parse:(e,t,n)=>({children:_e(t,e[2],n),id:i.slugify(e[2],Le),level:e[1].length}),render:(e,t,n)=>u(`h${e.level}`,{id:e.id,key:n.key},t(e.children,n))},[r.headingSetext]:{match:De(A),order:0,parse:(e,t,n)=>({children:_e(t,e[1],n),level:"="===e[2]?1:2,type:r.heading})},[r.htmlBlock]:{match:Ue(O),order:1,parse(e,t,n){const[,r]=e[3].match(le),i=new RegExp(`^${r}`,"gm"),l=e[3].replace(i,""),o=(c=l,Ee.some(e=>e.test(c))?We:_e);var c;const s=e[1].toLowerCase(),d=-1!==a.indexOf(s),u=(d?s:e[1]).trim(),p={attrs:q(u,e[2]),noInnerParse:d,tag:u};return n.inAnchor=n.inAnchor||"a"===s,d?p.text=e[3]:p.children=o(t,l,n),n.inAnchor=!1,p},render:(e,n,r)=>u(e.tag,t({key:r.key},e.attrs),e.text||(e.children?n(e.children,r):""))},[r.htmlSelfClosing]:{match:Ue(R),order:1,parse(e){const t=e[1].trim();return{attrs:q(t,e[2]||""),tag:t}},render:(e,n,r)=>u(e.tag,t({},e.attrs,{key:r.key}))},[r.htmlComment]:{match:Ue(B),order:1,parse:()=>({}),render:qe},[r.image]:{match:Ie(Se),order:1,parse:e=>({alt:e[1],target:Pe(e[2]),title:e[3]}),render:(e,t,n)=>u("img",{key:n.key,alt:e.alt||void 0,title:e.title||void 0,src:i.sanitizer(e.target,"img","src")})},[r.link]:{match:Re($e),order:3,parse:(e,t,n)=>({children:Fe(t,e[1],n),target:Pe(e[2]),title:e[3]}),render:(e,t,n)=>u("a",{key:n.key,href:i.sanitizer(e.target,"a","href"),title:e.title},t(e.children,n))},[r.linkAngleBraceStyleDetector]:{match:Re(N),order:0,parse:e=>({children:[{text:e[1],type:r.text}],target:e[1],type:r.link})},[r.linkBareUrlDetector]:{match:Me((e,t)=>t.inAnchor||i.disableAutoLink?null:Re(D)(e,t)),order:0,parse:e=>({children:[{text:e[1],type:r.text}],target:e[1],title:void 0,type:r.link})},[r.linkMailtoDetector]:{match:Re(U),order:0,parse(e){let t=e[1],n=e[1];return d.test(n)||(n="mailto:"+n),{children:[{text:t.replace("mailto:",""),type:r.text}],target:n,type:r.link}}},[r.orderedList]:Ce(u,1),[r.unorderedList]:Ce(u,2),[r.newlineCoalescer]:{match:De(b),order:3,parse:Ze,render:()=>"\n"},[r.paragraph]:{match:Me(Ne),order:3,parse:Ge,render:(e,t,n)=>u("p",{key:n.key},t(e.children,n))},[r.ref]:{match:Re(P),order:0,parse:e=>(V[e[1]]={target:e[2],title:e[4]},{}),render:qe},[r.refImage]:{match:Ie(_),order:0,parse:e=>({alt:e[1]||void 0,ref:e[2]}),render:(e,t,n)=>V[e.ref]?u("img",{key:n.key,alt:e.alt,src:i.sanitizer(V[e.ref].target,"img","src"),title:V[e.ref].title}):null},[r.refLink]:{match:Re(F),order:0,parse:(e,t,n)=>({children:t(e[1],n),fallbackChildren:e[0],ref:e[2]}),render:(e,t,n)=>V[e.ref]?u("a",{key:n.key,href:i.sanitizer(V[e.ref].target,"a","href"),title:V[e.ref].title},t(e.children,n)):u("span",{key:n.key},e.fallbackChildren)},[r.table]:{match:De(H),order:1,parse:Te,render(e,t,n){const r=e;return u("table",{key:n.key},u("thead",null,u("tr",null,r.header.map(function(e,i){return u("th",{key:i,style:Be(r,i)},t(e,n))}))),u("tbody",null,r.cells.map(function(e,i){return u("tr",{key:i},e.map(function(e,i){return u("td",{key:i,style:Be(r,i)},t(e,n))}))})))}},[r.text]:{match:Ue(re),order:4,parse:e=>({text:e[0].replace(T,(e,t)=>i.namedCodesToUnicode[t]?i.namedCodesToUnicode[t]:e)}),render:e=>e.text},[r.textBolded]:{match:Ie(J),order:2,parse:(e,t,n)=>({children:t(e[2],n)}),render:(e,t,n)=>u("strong",{key:n.key},t(e.children,n))},[r.textEmphasized]:{match:Ie(K),order:3,parse:(e,t,n)=>({children:t(e[2],n)}),render:(e,t,n)=>u("em",{key:n.key},t(e.children,n))},[r.textEscaped]:{match:Ie(te),order:1,parse:e=>({text:e[1],type:r.text})},[r.textMarked]:{match:Ie(Y),order:3,parse:Ge,render:(e,t,n)=>u("mark",{key:n.key},t(e.children,n))},[r.textStrikethroughed]:{match:Ie(ee),order:3,parse:Ge,render:(e,t,n)=>u("del",{key:n.key},t(e.children,n))}};!0===i.disableParsingRawHTML&&(delete X[r.htmlBlock],delete X[r.htmlSelfClosing]);const oe=function(e){let t=Object.keys(e);function n(r,i){let l,o,a=[],c="",s="";for(i.prevCapture=i.prevCapture||"";r;){let d=0;for(;d<t.length;){if(c=t[d],l=e[c],i.inline&&!l.match.inline){d++;continue}const u=l.match(r,i);if(u){s=u[0],i.prevCapture+=s,r=r.substring(s.length),o=l.parse(u,n,i),null==o.type&&(o.type=c),a.push(o);break}d++}}return i.prevCapture="",a}return t.sort(function(t,n){let r=e[t].order,i=e[n].order;return r!==i?r-i:t<n?-1:1}),function(e,t){return n(function(e){return e.replace(v,"\n").replace(S,"").replace(G,"    ")}(e),t)}}(X),ae=(ce=function(e,t){return function(n,r,i){const l=e[n.type].render;return t?t(()=>l(n,r,i),n,r,i):l(n,r,i)}}(X,i.renderRule),function e(t,n={}){if(Array.isArray(t)){const r=n.key,i=[];let l=!1;for(let r=0;r<t.length;r++){n.key=r;const o=e(t[r],n),a="string"==typeof o;a&&l?i[i.length-1]+=o:null!==o&&i.push(o),l=a}return n.key=r,i}return ce(t,e,n)});var ce;const se=Z(n);return Q.length?u("div",null,se,u("footer",{key:"footer"},Q.map(function(e){return u("div",{id:i.slugify(e.identifier,Le),key:e.identifier},e.identifier,ae(oe(e.footnote,{inline:!0})))}))):se}export default t=>{let{children:r="",options:i}=t,l=function(e,t){if(null==e)return{};var n,r,i={},l=Object.keys(e);for(r=0;r<l.length;r++)t.indexOf(n=l[r])>=0||(i[n]=e[n]);return i}(t,n);return e.cloneElement(Xe(r,i),l)};export{r as RuleType,Xe as compiler,He as sanitizer,Le as slugify};
//# sourceMappingURL=index.modern.js.map
