{"version": 3, "file": "index.module.js", "sources": ["../index.tsx"], "sourcesContent": ["/* @jsx h */\n/**\n * markdown-to-jsx is a fork of\n * [simple-markdown v0.2.2](https://github.com/Khan/simple-markdown)\n * from Khan Academy. Thank you <PERSON> devs for making such an awesome\n * and extensible parsing infra... without it, half of the\n * optimizations here wouldn't be feasible. 🙏🏼\n */\nimport * as React from 'react'\n\n/**\n * Analogous to `node.type`. Please note that the values here may change at any time,\n * so do not hard code against the value directly.\n */\nexport const RuleType = {\n  blockQuote: '0',\n  breakLine: '1',\n  breakThematic: '2',\n  codeBlock: '3',\n  codeFenced: '4',\n  codeInline: '5',\n  footnote: '6',\n  footnoteReference: '7',\n  gfmTask: '8',\n  heading: '9',\n  headingSetext: '10',\n  /** only available if not `disableHTMLParsing` */\n  htmlBlock: '11',\n  htmlComment: '12',\n  /** only available if not `disableHTMLParsing` */\n  htmlSelfClosing: '13',\n  image: '14',\n  link: '15',\n  /** emits a `link` 'node', does not render directly */\n  linkAngleBraceStyleDetector: '16',\n  /** emits a `link` 'node', does not render directly */\n  linkBareUrlDetector: '17',\n  /** emits a `link` 'node', does not render directly */\n  linkMailtoDetector: '18',\n  newlineCoalescer: '19',\n  orderedList: '20',\n  paragraph: '21',\n  ref: '22',\n  refImage: '23',\n  refLink: '24',\n  table: '25',\n  tableSeparator: '26',\n  text: '27',\n  textBolded: '28',\n  textEmphasized: '29',\n  textEscaped: '30',\n  textMarked: '31',\n  textStrikethroughed: '32',\n  unorderedList: '33',\n} as const\n\nif (process.env.NODE_ENV !== 'production') {\n  Object.keys(RuleType).forEach(key => (RuleType[key] = key))\n}\n\nexport type RuleType = (typeof RuleType)[keyof typeof RuleType]\n\nconst enum Priority {\n  /**\n   * anything that must scan the tree before everything else\n   */\n  MAX,\n  /**\n   * scans for block-level constructs\n   */\n  HIGH,\n  /**\n   * inline w/ more priority than other inline\n   */\n  MED,\n  /**\n   * inline elements\n   */\n  LOW,\n  /**\n   * bare text and stuff that is considered leftovers\n   */\n  MIN,\n}\n\n/** TODO: Drop for React 16? */\nconst ATTRIBUTE_TO_JSX_PROP_MAP = [\n  'allowFullScreen',\n  'allowTransparency',\n  'autoComplete',\n  'autoFocus',\n  'autoPlay',\n  'cellPadding',\n  'cellSpacing',\n  'charSet',\n  'classId',\n  'colSpan',\n  'contentEditable',\n  'contextMenu',\n  'crossOrigin',\n  'encType',\n  'formAction',\n  'formEncType',\n  'formMethod',\n  'formNoValidate',\n  'formTarget',\n  'frameBorder',\n  'hrefLang',\n  'inputMode',\n  'keyParams',\n  'keyType',\n  'marginHeight',\n  'marginWidth',\n  'maxLength',\n  'mediaGroup',\n  'minLength',\n  'noValidate',\n  'radioGroup',\n  'readOnly',\n  'rowSpan',\n  'spellCheck',\n  'srcDoc',\n  'srcLang',\n  'srcSet',\n  'tabIndex',\n  'useMap',\n].reduce(\n  (obj, x) => {\n    obj[x.toLowerCase()] = x\n    return obj\n  },\n  { class: 'className', for: 'htmlFor' }\n)\n\nconst namedCodesToUnicode = {\n  amp: '\\u0026',\n  apos: '\\u0027',\n  gt: '\\u003e',\n  lt: '\\u003c',\n  nbsp: '\\u00a0',\n  quot: '\\u201c',\n} as const\n\nconst DO_NOT_PROCESS_HTML_ELEMENTS = ['style', 'script']\nconst ATTRIBUTES_TO_SANITIZE = [\n  'src',\n  'href',\n  'data',\n  'formAction',\n  'srcDoc',\n  'action',\n]\n\n/**\n * the attribute extractor regex looks for a valid attribute name,\n * followed by an equal sign (whitespace around the equal sign is allowed), followed\n * by one of the following:\n *\n * 1. a single quote-bounded string, e.g. 'foo'\n * 2. a double quote-bounded string, e.g. \"bar\"\n * 3. an interpolation, e.g. {something}\n *\n * JSX can be be interpolated into itself and is passed through the compiler using\n * the same options and setup as the current run.\n *\n * <Something children={<SomeOtherThing />} />\n *                      ==================\n *                              ↳ children: [<SomeOtherThing />]\n *\n * Otherwise, interpolations are handled as strings or simple booleans\n * unless HTML syntax is detected.\n *\n * <Something color={green} disabled={true} />\n *                   =====            ====\n *                     ↓                ↳ disabled: true\n *                     ↳ color: \"green\"\n *\n * Numbers are not parsed at this time due to complexities around int, float,\n * and the upcoming bigint functionality that would make handling it unwieldy.\n * Parse the string in your component as desired.\n *\n * <Something someBigNumber={123456789123456789} />\n *                           ==================\n *                                   ↳ someBigNumber: \"123456789123456789\"\n */\nconst ATTR_EXTRACTOR_R =\n  /([-A-Z0-9_:]+)(?:\\s*=\\s*(?:(?:\"((?:\\\\.|[^\"])*)\")|(?:'((?:\\\\.|[^'])*)')|(?:\\{((?:\\\\.|{[^}]*?}|[^}])*)\\})))?/gi\n\n/** TODO: Write explainers for each of these */\n\nconst AUTOLINK_MAILTO_CHECK_R = /mailto:/i\nconst BLOCK_END_R = /\\n{2,}$/\nconst BLOCKQUOTE_R = /^(\\s*>[\\s\\S]*?)(?=\\n\\n|$)/\nconst BLOCKQUOTE_TRIM_LEFT_MULTILINE_R = /^ *> ?/gm\nconst BLOCKQUOTE_ALERT_R = /^(?:\\[!([^\\]]*)\\]\\n)?([\\s\\S]*)/\nconst BREAK_LINE_R = /^ {2,}\\n/\nconst BREAK_THEMATIC_R = /^(?:( *[-*_])){3,} *(?:\\n *)+\\n/\nconst CODE_BLOCK_FENCED_R =\n  /^(?: {1,3})?(`{3,}|~{3,}) *(\\S+)? *([^\\n]*?)?\\n([\\s\\S]*?)(?:\\1\\n?|$)/\nconst CODE_BLOCK_R = /^(?: {4}[^\\n]+\\n*)+(?:\\n *)+\\n?/\nconst CODE_INLINE_R = /^(`+)((?:\\\\`|(?!\\1)`|[^`])+)\\1/\nconst CONSECUTIVE_NEWLINE_R = /^(?:\\n *)*\\n/\nconst CR_NEWLINE_R = /\\r\\n?/g\n\n/**\n * Matches footnotes on the format:\n *\n * [^key]: value\n *\n * Matches multiline footnotes\n *\n * [^key]: row\n * row\n * row\n *\n * And empty lines in indented multiline footnotes\n *\n * [^key]: indented with\n *     row\n *\n *     row\n *\n * Explanation:\n *\n * 1. Look for the starting tag, eg: [^key]\n *    ^\\[\\^([^\\]]+)]\n *\n * 2. The first line starts with a colon, and continues for the rest of the line\n *   :(.*)\n *\n * 3. Parse as many additional lines as possible. Matches new non-empty lines that doesn't begin with a new footnote definition.\n *    (\\n(?!\\[\\^).+)\n *\n * 4. ...or allows for repeated newlines if the next line begins with at least four whitespaces.\n *    (\\n+ {4,}.*)\n */\nconst FOOTNOTE_R = /^\\[\\^([^\\]]+)](:(.*)((\\n+ {4,}.*)|(\\n(?!\\[\\^).+))*)/\n\nconst FOOTNOTE_REFERENCE_R = /^\\[\\^([^\\]]+)]/\nconst FORMFEED_R = /\\f/g\nconst FRONT_MATTER_R = /^---[ \\t]*\\n(.|\\n)*\\n---[ \\t]*\\n/\nconst GFM_TASK_R = /^\\s*?\\[(x|\\s)\\]/\nconst HEADING_R = /^ *(#{1,6}) *([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/\nconst HEADING_ATX_COMPLIANT_R =\n  /^ *(#{1,6}) +([^\\n]+?)(?: +#*)?(?:\\n *)*(?:\\n|$)/\nconst HEADING_SETEXT_R = /^([^\\n]+)\\n *(=|-){3,} *(?:\\n *)+\\n/\n\n/**\n * Explanation:\n *\n * 1. Look for a starting tag, preceded by any amount of spaces\n *    ^ *<\n *\n * 2. Capture the tag name (capture 1)\n *    ([^ >/]+)\n *\n * 3. Ignore a space after the starting tag and capture the attribute portion of the tag (capture 2)\n *     ?([^>]*)>\n *\n * 4. Ensure a matching closing tag is present in the rest of the input string\n *    (?=[\\s\\S]*<\\/\\1>)\n *\n * 5. Capture everything until the matching closing tag -- this might include additional pairs\n *    of the same tag type found in step 2 (capture 3)\n *    ((?:[\\s\\S]*?(?:<\\1[^>]*>[\\s\\S]*?<\\/\\1>)*[\\s\\S]*?)*?)<\\/\\1>\n *\n * 6. Capture excess newlines afterward\n *    \\n*\n */\nconst HTML_BLOCK_ELEMENT_R =\n  /^ *(?!<[a-z][^ >/]* ?\\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\\n?(\\s*(?:<\\1[^>]*?>[\\s\\S]*?<\\/\\1>|(?!<\\1\\b)[\\s\\S])*?)<\\/\\1>(?!<\\/\\1>)\\n*/i\n\nconst HTML_CHAR_CODE_R = /&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi\n\nconst HTML_COMMENT_R = /^<!--[\\s\\S]*?(?:-->)/\n\n/**\n * borrowed from React 15(https://github.com/facebook/react/blob/894d20744cba99383ffd847dbd5b6e0800355a5c/src/renderers/dom/shared/HTMLDOMPropertyConfig.js)\n */\nconst HTML_CUSTOM_ATTR_R = /^(data|aria|x)-[a-z_][a-z\\d_.-]*$/\n\nconst HTML_SELF_CLOSING_ELEMENT_R =\n  /^ *<([a-z][a-z0-9:]*)(?:\\s+((?:<.*?>|[^>])*))?\\/?>(?!<\\/\\1>)(\\s*\\n)?/i\nconst INTERPOLATION_R = /^\\{.*\\}$/\nconst LINK_AUTOLINK_BARE_URL_R = /^(https?:\\/\\/[^\\s<]+[^<.,:;\"')\\]\\s])/\nconst LINK_AUTOLINK_MAILTO_R = /^<([^ >]+@[^ >]+)>/\nconst LINK_AUTOLINK_R = /^<([^ >]+:\\/[^ >]+)>/\nconst CAPTURE_LETTER_AFTER_HYPHEN = /-([a-z])?/gi\nconst NP_TABLE_R = /^(\\|.*)\\n(?: *(\\|? *[-:]+ *\\|[-| :]*)\\n((?:.*\\|.*\\n)*))?\\n?/\nconst PARAGRAPH_R = /^[^\\n]+(?:  \\n|\\n{2,})/\nconst REFERENCE_IMAGE_OR_LINK = /^\\[([^\\]]*)\\]:\\s+<?([^\\s>]+)>?\\s*(\"([^\"]*)\")?/\nconst REFERENCE_IMAGE_R = /^!\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/\nconst REFERENCE_LINK_R = /^\\[([^\\]]*)\\] ?\\[([^\\]]*)\\]/\nconst SHOULD_RENDER_AS_BLOCK_R = /(\\n|^[-*]\\s|^#|^ {2,}|^-{2,}|^>\\s)/\nconst TAB_R = /\\t/g\nconst TABLE_TRIM_PIPES = /(^ *\\||\\| *$)/g\nconst TABLE_CENTER_ALIGN = /^ *:-+: *$/\nconst TABLE_LEFT_ALIGN = /^ *:-+ *$/\nconst TABLE_RIGHT_ALIGN = /^ *-+: *$/\n\n/**\n * For inline formatting, this partial attempts to ignore characters that\n * may appear in nested formatting that could prematurely trigger detection\n * and therefore miss content that should have been included.\n */\nconst INLINE_SKIP_R =\n  '((?:\\\\[.*?\\\\][([].*?[)\\\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\\\\\\\1|[\\\\s\\\\S])+?)'\n\n/**\n * Detect a sequence like **foo** or __foo__. Note that bold has a higher priority\n * than emphasized to support nesting of both since they share a delimiter.\n */\nconst TEXT_BOLD_R = new RegExp(`^([*_])\\\\1${INLINE_SKIP_R}\\\\1\\\\1(?!\\\\1)`)\n\n/**\n * Detect a sequence like *foo* or _foo_.\n */\nconst TEXT_EMPHASIZED_R = new RegExp(`^([*_])${INLINE_SKIP_R}\\\\1(?!\\\\1)`)\n\n/**\n * Detect a sequence like ==foo==.\n */\nconst TEXT_MARKED_R = new RegExp(`^(==)${INLINE_SKIP_R}\\\\1`)\n\n/**\n * Detect a sequence like ~~foo~~.\n */\nconst TEXT_STRIKETHROUGHED_R = new RegExp(`^(~~)${INLINE_SKIP_R}\\\\1`)\n\nconst TEXT_ESCAPED_R = /^\\\\([^0-9A-Za-z\\s])/\nconst TEXT_UNESCAPE_R = /\\\\([^0-9A-Za-z\\s])/g\n\n/**\n * Always take the first character, then eagerly take text until a double space\n * (potential line break) or some markdown-like punctuation is reached.\n */\nconst TEXT_PLAIN_R = /^([\\s\\S](?:(?!  |[0-9]\\.)[^=*_~\\-\\n<`\\\\\\[!])*)/\n\nconst TRIM_STARTING_NEWLINES = /^\\n+/\n\nconst HTML_LEFT_TRIM_AMOUNT_R = /^([ \\t]*)/\n\nconst UNESCAPE_URL_R = /\\\\([^\\\\])/g\n\ntype LIST_TYPE = 1 | 2\nconst ORDERED: LIST_TYPE = 1\nconst UNORDERED: LIST_TYPE = 2\n\nconst LIST_ITEM_END_R = / *\\n+$/\nconst LIST_LOOKBEHIND_R = /(?:^|\\n)( *)$/\n\n// recognize a `*` `-`, `+`, `1.`, `2.`... list bullet\nconst ORDERED_LIST_BULLET = '(?:\\\\d+\\\\.)'\nconst UNORDERED_LIST_BULLET = '(?:[*+-])'\n\nfunction generateListItemPrefix(type: LIST_TYPE) {\n  return (\n    '( *)(' +\n    (type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET) +\n    ') +'\n  )\n}\n\n// recognize the start of a list item:\n// leading space plus a bullet plus a space (`   * `)\nconst ORDERED_LIST_ITEM_PREFIX = generateListItemPrefix(ORDERED)\nconst UNORDERED_LIST_ITEM_PREFIX = generateListItemPrefix(UNORDERED)\n\nfunction generateListItemPrefixRegex(type: LIST_TYPE) {\n  return new RegExp(\n    '^' +\n      (type === ORDERED ? ORDERED_LIST_ITEM_PREFIX : UNORDERED_LIST_ITEM_PREFIX)\n  )\n}\n\nconst ORDERED_LIST_ITEM_PREFIX_R = generateListItemPrefixRegex(ORDERED)\nconst UNORDERED_LIST_ITEM_PREFIX_R = generateListItemPrefixRegex(UNORDERED)\n\nfunction generateListItemRegex(type: LIST_TYPE) {\n  // recognize an individual list item:\n  //  * hi\n  //    this is part of the same item\n  //\n  //    as is this, which is a new paragraph in the same item\n  //\n  //  * but this is not part of the same item\n  return new RegExp(\n    '^' +\n      (type === ORDERED\n        ? ORDERED_LIST_ITEM_PREFIX\n        : UNORDERED_LIST_ITEM_PREFIX) +\n      '[^\\\\n]*(?:\\\\n' +\n      '(?!\\\\1' +\n      (type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET) +\n      ' )[^\\\\n]*)*(\\\\n|$)',\n    'gm'\n  )\n}\n\nconst ORDERED_LIST_ITEM_R = generateListItemRegex(ORDERED)\nconst UNORDERED_LIST_ITEM_R = generateListItemRegex(UNORDERED)\n\n// check whether a list item has paragraphs: if it does,\n// we leave the newlines at the end\nfunction generateListRegex(type: LIST_TYPE) {\n  const bullet = type === ORDERED ? ORDERED_LIST_BULLET : UNORDERED_LIST_BULLET\n\n  return new RegExp(\n    '^( *)(' +\n      bullet +\n      ') ' +\n      '[\\\\s\\\\S]+?(?:\\\\n{2,}(?! )' +\n      '(?!\\\\1' +\n      bullet +\n      ' (?!' +\n      bullet +\n      ' ))\\\\n*' +\n      // the \\\\s*$ here is so that we can parse the inside of nested\n      // lists, where our content might end before we receive two `\\n`s\n      '|\\\\s*\\\\n*$)'\n  )\n}\n\nconst ORDERED_LIST_R = generateListRegex(ORDERED)\nconst UNORDERED_LIST_R = generateListRegex(UNORDERED)\n\nfunction generateListRule(\n  h: any,\n  type: LIST_TYPE\n): MarkdownToJSX.Rule<\n  MarkdownToJSX.OrderedListNode | MarkdownToJSX.UnorderedListNode\n> {\n  const ordered = type === ORDERED\n  const LIST_R = ordered ? ORDERED_LIST_R : UNORDERED_LIST_R\n  const LIST_ITEM_R = ordered ? ORDERED_LIST_ITEM_R : UNORDERED_LIST_ITEM_R\n  const LIST_ITEM_PREFIX_R = ordered\n    ? ORDERED_LIST_ITEM_PREFIX_R\n    : UNORDERED_LIST_ITEM_PREFIX_R\n\n  return {\n    match: allowInline(function (source, state) {\n      // We only want to break into a list if we are at the start of a\n      // line. This is to avoid parsing \"hi * there\" with \"* there\"\n      // becoming a part of a list.\n      // You might wonder, \"but that's inline, so of course it wouldn't\n      // start a list?\". You would be correct! Except that some of our\n      // lists can be inline, because they might be inside another list,\n      // in which case we can parse with inline scope, but need to allow\n      // nested lists inside this inline scope.\n      const isStartOfLine = LIST_LOOKBEHIND_R.exec(state.prevCapture)\n      const isListAllowed = state.list || (!state.inline && !state.simple)\n\n      if (isStartOfLine && isListAllowed) {\n        source = isStartOfLine[1] + source\n\n        return LIST_R.exec(source)\n      } else {\n        return null\n      }\n    }),\n    order: Priority.HIGH,\n    parse(capture, parse, state) {\n      const bullet = capture[2]\n      const start = ordered ? +bullet : undefined\n      const items = capture[0]\n        // recognize the end of a paragraph block inside a list item:\n        // two or more newlines at end end of the item\n        .replace(BLOCK_END_R, '\\n')\n        .match(LIST_ITEM_R)\n\n      let lastItemWasAParagraph = false\n\n      const itemContent = items.map(function (item, i) {\n        // We need to see how far indented the item is:\n        const space = LIST_ITEM_PREFIX_R.exec(item)[0].length\n\n        // And then we construct a regex to \"unindent\" the subsequent\n        // lines of the items by that amount:\n        const spaceRegex = new RegExp('^ {1,' + space + '}', 'gm')\n\n        // Before processing the item, we need a couple things\n        const content = item\n          // remove indents on trailing lines:\n          .replace(spaceRegex, '')\n          // remove the bullet:\n          .replace(LIST_ITEM_PREFIX_R, '')\n\n        // Handling \"loose\" lists, like:\n        //\n        //  * this is wrapped in a paragraph\n        //\n        //  * as is this\n        //\n        //  * as is this\n        const isLastItem = i === items.length - 1\n        const containsBlocks = content.indexOf('\\n\\n') !== -1\n\n        // Any element in a list is a block if it contains multiple\n        // newlines. The last element in the list can also be a block\n        // if the previous item in the list was a block (this is\n        // because non-last items in the list can end with \\n\\n, but\n        // the last item can't, so we just \"inherit\" this property\n        // from our previous element).\n        const thisItemIsAParagraph =\n          containsBlocks || (isLastItem && lastItemWasAParagraph)\n        lastItemWasAParagraph = thisItemIsAParagraph\n\n        // backup our state for delta afterwards. We're going to\n        // want to set state.list to true, and state.inline depending\n        // on our list's looseness.\n        const oldStateInline = state.inline\n        const oldStateList = state.list\n        state.list = true\n\n        // Parse inline if we're in a tight list, or block if we're in\n        // a loose list.\n        let adjustedContent\n        if (thisItemIsAParagraph) {\n          state.inline = false\n          adjustedContent = trimEnd(content) + '\\n\\n'\n        } else {\n          state.inline = true\n          adjustedContent = trimEnd(content)\n        }\n\n        const result = parse(adjustedContent, state)\n\n        // Restore our state before returning\n        state.inline = oldStateInline\n        state.list = oldStateList\n\n        return result\n      })\n\n      return {\n        items: itemContent,\n        ordered: ordered,\n        start: start,\n      }\n    },\n    render(node, output, state) {\n      const Tag = node.ordered ? 'ol' : 'ul'\n\n      return (\n        <Tag\n          key={state.key}\n          start={node.type === RuleType.orderedList ? node.start : undefined}\n        >\n          {node.items.map(function generateListItem(item, i) {\n            return <li key={i}>{output(item, state)}</li>\n          })}\n        </Tag>\n      )\n    },\n  }\n}\n\nconst LINK_INSIDE = '(?:\\\\[[^\\\\]]*\\\\]|[^\\\\[\\\\]]|\\\\](?=[^\\\\[]*\\\\]))*'\nconst LINK_HREF_AND_TITLE =\n  '\\\\s*<?((?:\\\\([^)]*\\\\)|[^\\\\s\\\\\\\\]|\\\\\\\\.)*?)>?(?:\\\\s+[\\'\"]([\\\\s\\\\S]*?)[\\'\"])?\\\\s*'\nconst LINK_R = new RegExp(\n  '^\\\\[(' + LINK_INSIDE + ')\\\\]\\\\(' + LINK_HREF_AND_TITLE + '\\\\)'\n)\nconst IMAGE_R = /^!\\[(.*?)\\]\\( *((?:\\([^)]*\\)|[^() ])*) *\"?([^)\"]*)?\"?\\)/\n\nconst NON_PARAGRAPH_BLOCK_SYNTAXES = [\n  BLOCKQUOTE_R,\n  CODE_BLOCK_FENCED_R,\n  CODE_BLOCK_R,\n  HEADING_R,\n  HEADING_SETEXT_R,\n  HEADING_ATX_COMPLIANT_R,\n  NP_TABLE_R,\n  ORDERED_LIST_R,\n  UNORDERED_LIST_R,\n]\n\nconst BLOCK_SYNTAXES = [\n  ...NON_PARAGRAPH_BLOCK_SYNTAXES,\n  PARAGRAPH_R,\n  HTML_BLOCK_ELEMENT_R,\n  HTML_COMMENT_R,\n  HTML_SELF_CLOSING_ELEMENT_R,\n]\n\nfunction trimEnd(str: string) {\n  let end = str.length\n  while (end > 0 && str[end - 1] <= ' ') end--\n  return str.slice(0, end)\n}\n\nfunction containsBlockSyntax(input: string) {\n  return BLOCK_SYNTAXES.some(r => r.test(input))\n}\n\n/** Remove symmetrical leading and trailing quotes */\nfunction unquote(str: string) {\n  const first = str[0]\n  if (\n    (first === '\"' || first === \"'\") &&\n    str.length >= 2 &&\n    str[str.length - 1] === first\n  ) {\n    return str.slice(1, -1)\n  }\n  return str\n}\n\n// based on https://stackoverflow.com/a/18123682/1141611\n// not complete, but probably good enough\nexport function slugify(str: string) {\n  return str\n    .replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g, 'a')\n    .replace(/[çÇ]/g, 'c')\n    .replace(/[ðÐ]/g, 'd')\n    .replace(/[ÈÉÊËéèêë]/g, 'e')\n    .replace(/[ÏïÎîÍíÌì]/g, 'i')\n    .replace(/[Ññ]/g, 'n')\n    .replace(/[øØœŒÕõÔôÓóÒò]/g, 'o')\n    .replace(/[ÜüÛûÚúÙù]/g, 'u')\n    .replace(/[ŸÿÝý]/g, 'y')\n    .replace(/[^a-z0-9- ]/gi, '')\n    .replace(/ /gi, '-')\n    .toLowerCase()\n}\n\nfunction parseTableAlignCapture(alignCapture: string) {\n  if (TABLE_RIGHT_ALIGN.test(alignCapture)) {\n    return 'right'\n  } else if (TABLE_CENTER_ALIGN.test(alignCapture)) {\n    return 'center'\n  } else if (TABLE_LEFT_ALIGN.test(alignCapture)) {\n    return 'left'\n  }\n\n  return null\n}\n\nfunction parseTableRow(\n  source: string,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State,\n  tableOutput: boolean\n): MarkdownToJSX.ParserResult[][] {\n  const prevInTable = state.inTable\n\n  state.inTable = true\n\n  let cells: MarkdownToJSX.ParserResult[][] = [[]]\n  let acc = ''\n\n  function flush() {\n    if (!acc) return\n\n    const cell = cells[cells.length - 1]\n    cell.push.apply(cell, parse(acc, state))\n    acc = ''\n  }\n\n  source\n    .trim()\n    // isolate situations where a pipe should be ignored (inline code, escaped, etc)\n    .split(/(`[^`]*`|\\\\\\||\\|)/)\n    .filter(Boolean)\n    .forEach((fragment, i, arr) => {\n      if (fragment.trim() === '|') {\n        flush()\n\n        if (tableOutput) {\n          if (i !== 0 && i !== arr.length - 1) {\n            // Split the current row\n            cells.push([])\n          }\n\n          return\n        }\n      }\n\n      acc += fragment\n    })\n\n  flush()\n\n  state.inTable = prevInTable\n\n  return cells\n}\n\nfunction parseTableAlign(source: string /*, parse, state*/) {\n  const alignText = source.replace(TABLE_TRIM_PIPES, '').split('|')\n\n  return alignText.map(parseTableAlignCapture)\n}\n\nfunction parseTableCells(\n  source: string,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State\n) {\n  const rowsText = source.trim().split('\\n')\n\n  return rowsText.map(function (rowText) {\n    return parseTableRow(rowText, parse, state, true)\n  })\n}\n\nfunction parseTable(\n  capture: RegExpMatchArray,\n  parse: MarkdownToJSX.NestedParser,\n  state: MarkdownToJSX.State\n) {\n  /**\n   * The table syntax makes some other parsing angry so as a bit of a hack even if alignment and/or cell rows are missing,\n   * we'll still run a detected first row through the parser and then just emit a paragraph.\n   */\n  state.inline = true\n  const align = capture[2] ? parseTableAlign(capture[2]) : []\n  const cells = capture[3] ? parseTableCells(capture[3], parse, state) : []\n  const header = parseTableRow(capture[1], parse, state, !!cells.length)\n  state.inline = false\n\n  return cells.length\n    ? {\n        align: align,\n        cells: cells,\n        header: header,\n        type: RuleType.table,\n      }\n    : {\n        children: header,\n        type: RuleType.paragraph,\n      }\n}\n\nfunction getTableStyle(node, colIndex) {\n  return node.align[colIndex] == null\n    ? {}\n    : {\n        textAlign: node.align[colIndex],\n      }\n}\n\n/** TODO: remove for react 16 */\nfunction normalizeAttributeKey(key) {\n  const hyphenIndex = key.indexOf('-')\n\n  if (hyphenIndex !== -1 && key.match(HTML_CUSTOM_ATTR_R) === null) {\n    key = key.replace(CAPTURE_LETTER_AFTER_HYPHEN, function (_, letter) {\n      return letter.toUpperCase()\n    })\n  }\n\n  return key\n}\n\ntype StyleTuple = [key: string, value: string]\n\nfunction parseStyleAttribute(styleString: string): StyleTuple[] {\n  const styles: StyleTuple[] = []\n  let buffer = ''\n  let inUrl = false\n  let inQuotes = false\n  let quoteChar: '\"' | \"'\" | '' = ''\n\n  if (!styleString) return styles\n\n  for (let i = 0; i < styleString.length; i++) {\n    const char = styleString[i]\n\n    // Handle quotes\n    if ((char === '\"' || char === \"'\") && !inUrl) {\n      if (!inQuotes) {\n        inQuotes = true\n        quoteChar = char\n      } else if (char === quoteChar) {\n        inQuotes = false\n        quoteChar = ''\n      }\n    }\n\n    // Track url() values\n    if (char === '(' && buffer.endsWith('url')) {\n      inUrl = true\n    } else if (char === ')' && inUrl) {\n      inUrl = false\n    }\n\n    // Only split on semicolons when not in quotes or url()\n    if (char === ';' && !inQuotes && !inUrl) {\n      const declaration = buffer.trim()\n      if (declaration) {\n        const colonIndex = declaration.indexOf(':')\n        if (colonIndex > 0) {\n          const key = declaration.slice(0, colonIndex).trim()\n          const value = declaration.slice(colonIndex + 1).trim()\n          styles.push([key, value])\n        }\n      }\n      buffer = ''\n    } else {\n      buffer += char\n    }\n  }\n\n  // Handle the last declaration\n  const declaration = buffer.trim()\n  if (declaration) {\n    const colonIndex = declaration.indexOf(':')\n    if (colonIndex > 0) {\n      const key = declaration.slice(0, colonIndex).trim()\n      const value = declaration.slice(colonIndex + 1).trim()\n      styles.push([key, value])\n    }\n  }\n\n  return styles\n}\n\nfunction attributeValueToJSXPropValue(\n  tag: MarkdownToJSX.HTMLTags,\n  key: keyof React.AllHTMLAttributes<Element>,\n  value: string,\n  sanitizeUrlFn: MarkdownToJSX.Options['sanitizer']\n): any {\n  if (key === 'style') {\n    return parseStyleAttribute(value).reduce(function (styles, [key, value]) {\n      // snake-case to camelCase\n      // also handles PascalCasing vendor prefixes\n      const camelCasedKey = key.replace(/(-[a-z])/g, substr =>\n        substr[1].toUpperCase()\n      )\n\n      // key.length + 1 to skip over the colon\n      styles[camelCasedKey] = sanitizeUrlFn(value, tag, key)\n\n      return styles\n    }, {})\n  } else if (ATTRIBUTES_TO_SANITIZE.indexOf(key) !== -1) {\n    return sanitizeUrlFn(value, tag, key)\n  } else if (value.match(INTERPOLATION_R)) {\n    // return as a string and let the consumer decide what to do with it\n    value = value.slice(1, value.length - 1)\n  }\n\n  if (value === 'true') {\n    return true\n  } else if (value === 'false') {\n    return false\n  }\n\n  return value\n}\n\nfunction normalizeWhitespace(source: string): string {\n  return source\n    .replace(CR_NEWLINE_R, '\\n')\n    .replace(FORMFEED_R, '')\n    .replace(TAB_R, '    ')\n}\n\n/**\n * Creates a parser for a given set of rules, with the precedence\n * specified as a list of rules.\n *\n * @rules: an object containing\n * rule type -> {match, order, parse} objects\n * (lower order is higher precedence)\n * (Note: `order` is added to defaultRules after creation so that\n *  the `order` of defaultRules in the source matches the `order`\n *  of defaultRules in terms of `order` fields.)\n *\n * @returns The resulting parse function, with the following parameters:\n *   @source: the input source string to be parsed\n *   @state: an optional object to be threaded through parse\n *     calls. Allows clients to add stateful operations to\n *     parsing, such as keeping track of how many levels deep\n *     some nesting is. For an example use-case, see passage-ref\n *     parsing in src/widgets/passage/passage-markdown.jsx\n */\nfunction parserFor(\n  rules: MarkdownToJSX.Rules\n): (\n  source: string,\n  state: MarkdownToJSX.State\n) => ReturnType<MarkdownToJSX.NestedParser> {\n  // Sorts rules in order of increasing order, then\n  // ascending rule name in case of ties.\n  let ruleList = Object.keys(rules)\n\n  if (process.env.NODE_ENV !== 'production') {\n    ruleList.forEach(function (type) {\n      let order = rules[type].order\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        (typeof order !== 'number' || !isFinite(order))\n      ) {\n        console.warn(\n          'markdown-to-jsx: Invalid order for rule `' + type + '`: ' + order\n        )\n      }\n    })\n  }\n\n  ruleList.sort(function (typeA, typeB) {\n    let orderA = rules[typeA].order\n    let orderB = rules[typeB].order\n\n    // Sort based on increasing order\n    if (orderA !== orderB) {\n      return orderA - orderB\n    } else if (typeA < typeB) {\n      return -1\n    }\n\n    return 1\n  })\n\n  function nestedParse(\n    source: string,\n    state: MarkdownToJSX.State\n  ): MarkdownToJSX.ParserResult[] {\n    let result = []\n    let rule\n    let ruleType = ''\n    let parsed\n    let currCaptureString = ''\n\n    state.prevCapture = state.prevCapture || ''\n\n    // We store the previous capture so that match functions can\n    // use some limited amount of lookbehind. Lists use this to\n    // ensure they don't match arbitrary '- ' or '* ' in inline\n    // text (see the list rule for more information).\n    while (source) {\n      let i = 0\n      while (i < ruleList.length) {\n        ruleType = ruleList[i]\n        rule = rules[ruleType]\n\n        if (state.inline && !rule.match.inline) {\n          i++\n          continue\n        }\n\n        const capture = rule.match(source, state)\n\n        if (capture) {\n          currCaptureString = capture[0]\n\n          // retain what's been processed so far for lookbacks\n          state.prevCapture += currCaptureString\n\n          source = source.substring(currCaptureString.length)\n\n          parsed = rule.parse(capture, nestedParse, state)\n\n          // We also let rules override the default type of\n          // their parsed node if they would like to, so that\n          // there can be a single output function for all links,\n          // even if there are several rules to parse them.\n          if (parsed.type == null) {\n            parsed.type = ruleType as unknown as RuleType\n          }\n\n          result.push(parsed)\n          break\n        }\n\n        i++\n      }\n    }\n\n    // reset on exit\n    state.prevCapture = ''\n\n    return result\n  }\n\n  return function outerParse(source, state) {\n    return nestedParse(normalizeWhitespace(source), state)\n  }\n}\n\n/**\n * Marks a matcher function as eligible for being run inside an inline context;\n * allows us to do a little less work in the nested parser.\n */\nfunction allowInline<T extends Function & { inline?: 0 | 1 }>(fn: T) {\n  fn.inline = 1\n\n  return fn\n}\n\n// Creates a match function for an inline scoped or simple element from a regex\nfunction inlineRegex(regex: RegExp) {\n  return allowInline(function match(source, state: MarkdownToJSX.State) {\n    if (state.inline) {\n      return regex.exec(source)\n    } else {\n      return null\n    }\n  })\n}\n\n// basically any inline element except links\nfunction simpleInlineRegex(regex: RegExp) {\n  return allowInline(function match(\n    source: string,\n    state: MarkdownToJSX.State\n  ) {\n    if (state.inline || state.simple) {\n      return regex.exec(source)\n    } else {\n      return null\n    }\n  })\n}\n\n// Creates a match function for a block scoped element from a regex\nfunction blockRegex(regex: RegExp) {\n  return function match(source: string, state: MarkdownToJSX.State) {\n    if (state.inline || state.simple) {\n      return null\n    } else {\n      return regex.exec(source)\n    }\n  }\n}\n\n// Creates a match function from a regex, ignoring block/inline scope\nfunction anyScopeRegex(regex: RegExp) {\n  return allowInline(function match(source: string /*, state*/) {\n    return regex.exec(source)\n  })\n}\n\nfunction matchParagraph(source: string, state: MarkdownToJSX.State) {\n  if (state.inline || state.simple) {\n    return null\n  }\n\n  let match = ''\n\n  source.split('\\n').every(line => {\n    line += '\\n'\n\n    // bail out on first sign of non-paragraph block\n    if (NON_PARAGRAPH_BLOCK_SYNTAXES.some(regex => regex.test(line))) {\n      return false\n    }\n\n    match += line\n\n    return !!line.trim()\n  })\n\n  const captured = trimEnd(match)\n  if (captured == '') {\n    return null\n  }\n\n  // parseCaptureInline expects the inner content to be at index 2\n  // because index 1 is the delimiter for text formatting syntaxes\n  return [match, , captured]\n}\n\nconst SANITIZE_R = /(javascript|vbscript|data(?!:image)):/i\n\nexport function sanitizer(input: string): string {\n  try {\n    const decoded = decodeURIComponent(input).replace(/[^A-Za-z0-9/:]/g, '')\n\n    if (SANITIZE_R.test(decoded)) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          'Input contains an unsafe JavaScript/VBScript/data expression, it will not be rendered.',\n          decoded\n        )\n      }\n\n      return null\n    }\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(\n        'Input could not be decoded due to malformed syntax or characters, it will not be rendered.',\n        input\n      )\n    }\n\n    // decodeURIComponent sometimes throws a URIError\n    // See `decodeURIComponent('a%AFc');`\n    // http://stackoverflow.com/questions/9064536/javascript-decodeuricomponent-malformed-uri-exception\n    return null\n  }\n\n  return input\n}\n\nfunction unescapeUrl(rawUrlString: string): string {\n  return rawUrlString.replace(UNESCAPE_URL_R, '$1')\n}\n\n/**\n * Everything inline, including links.\n */\nfunction parseInline(\n  parse: MarkdownToJSX.NestedParser,\n  children: string,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  const isCurrentlySimple = state.simple || false\n  state.inline = true\n  state.simple = true\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  state.simple = isCurrentlySimple\n  return result\n}\n\n/**\n * Anything inline that isn't a link.\n */\nfunction parseSimpleInline(\n  parse: MarkdownToJSX.NestedParser,\n  children: string,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  const isCurrentlySimple = state.simple || false\n  state.inline = false\n  state.simple = true\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  state.simple = isCurrentlySimple\n  return result\n}\n\nfunction parseBlock(\n  parse,\n  children,\n  state: MarkdownToJSX.State\n): MarkdownToJSX.ParserResult[] {\n  const isCurrentlyInline = state.inline || false\n  state.inline = false\n  const result = parse(children, state)\n  state.inline = isCurrentlyInline\n  return result\n}\n\nconst parseCaptureInline: MarkdownToJSX.Parser<{\n  children: MarkdownToJSX.ParserResult[]\n}> = (capture, parse, state: MarkdownToJSX.State) => {\n  return {\n    children: parseInline(parse, capture[2], state),\n  }\n}\n\nfunction captureNothing() {\n  return {}\n}\n\nfunction renderNothing() {\n  return null\n}\n\nfunction reactFor(render) {\n  return function patchedRender(\n    ast: MarkdownToJSX.ParserResult | MarkdownToJSX.ParserResult[],\n    state: MarkdownToJSX.State = {}\n  ): React.ReactNode[] {\n    if (Array.isArray(ast)) {\n      const oldKey = state.key\n      const result = []\n\n      // map nestedOutput over the ast, except group any text\n      // nodes together into a single string output.\n      let lastWasString = false\n\n      for (let i = 0; i < ast.length; i++) {\n        state.key = i\n\n        const nodeOut = patchedRender(ast[i], state)\n        const isString = typeof nodeOut === 'string'\n\n        if (isString && lastWasString) {\n          result[result.length - 1] += nodeOut\n        } else if (nodeOut !== null) {\n          result.push(nodeOut)\n        }\n\n        lastWasString = isString\n      }\n\n      state.key = oldKey\n\n      return result\n    }\n\n    return render(ast, patchedRender, state)\n  }\n}\n\nfunction createRenderer(\n  rules: MarkdownToJSX.Rules,\n  userRender?: MarkdownToJSX.Options['renderRule']\n) {\n  return function renderRule(\n    ast: MarkdownToJSX.ParserResult,\n    render: MarkdownToJSX.RuleOutput,\n    state: MarkdownToJSX.State\n  ): React.ReactNode {\n    const renderer = rules[ast.type].render as MarkdownToJSX.Rule['render']\n\n    return userRender\n      ? userRender(() => renderer(ast, render, state), ast, render, state)\n      : renderer(ast, render, state)\n  }\n}\n\nfunction cx(...args) {\n  return args.filter(Boolean).join(' ')\n}\n\nfunction get(src: Object, path: string, fb?: any) {\n  let ptr = src\n  const frags = path.split('.')\n\n  while (frags.length) {\n    ptr = ptr[frags[0]]\n\n    if (ptr === undefined) break\n    else frags.shift()\n  }\n\n  return ptr || fb\n}\n\nfunction getTag(tag: string, overrides: MarkdownToJSX.Overrides) {\n  const override = get(overrides, tag)\n\n  if (!override) return tag\n\n  return typeof override === 'function' ||\n    (typeof override === 'object' && 'render' in override)\n    ? override\n    : get(overrides, `${tag}.component`, tag)\n}\n\nexport function compiler(\n  markdown: string = '',\n  options: MarkdownToJSX.Options = {}\n): React.JSX.Element {\n  options.overrides = options.overrides || {}\n  options.sanitizer = options.sanitizer || sanitizer\n  options.slugify = options.slugify || slugify\n  options.namedCodesToUnicode = options.namedCodesToUnicode\n    ? { ...namedCodesToUnicode, ...options.namedCodesToUnicode }\n    : namedCodesToUnicode\n\n  options.createElement = options.createElement || React.createElement\n\n  // JSX custom pragma\n  // eslint-disable-next-line no-unused-vars\n  function h(\n    // locally we always will render a known string tag\n    tag: MarkdownToJSX.HTMLTags,\n    props: Parameters<MarkdownToJSX.CreateElement>[1] & {\n      className?: string\n      id?: string\n    },\n    ...children\n  ) {\n    const overrideProps = get(options.overrides, `${tag}.props`, {})\n\n    return options.createElement(\n      getTag(tag, options.overrides),\n      {\n        ...props,\n        ...overrideProps,\n        className: cx(props?.className, overrideProps.className) || undefined,\n      },\n      ...children\n    )\n  }\n\n  function compile(input: string): React.JSX.Element {\n    input = input.replace(FRONT_MATTER_R, '')\n\n    let inline = false\n\n    if (options.forceInline) {\n      inline = true\n    } else if (!options.forceBlock) {\n      /**\n       * should not contain any block-level markdown like newlines, lists, headings,\n       * thematic breaks, blockquotes, tables, etc\n       */\n      inline = SHOULD_RENDER_AS_BLOCK_R.test(input) === false\n    }\n\n    const arr = emitter(\n      parser(\n        inline\n          ? input\n          : `${trimEnd(input).replace(TRIM_STARTING_NEWLINES, '')}\\n\\n`,\n        {\n          inline,\n        }\n      )\n    )\n\n    while (\n      typeof arr[arr.length - 1] === 'string' &&\n      !arr[arr.length - 1].trim()\n    ) {\n      arr.pop()\n    }\n\n    if (options.wrapper === null) {\n      return arr\n    }\n\n    const wrapper = options.wrapper || (inline ? 'span' : 'div')\n    let jsx\n\n    if (arr.length > 1 || options.forceWrapper) {\n      jsx = arr\n    } else if (arr.length === 1) {\n      jsx = arr[0]\n\n      // TODO: remove this for React 16\n      if (typeof jsx === 'string') {\n        return <span key=\"outer\">{jsx}</span>\n      } else {\n        return jsx\n      }\n    } else {\n      // TODO: return null for React 16\n      jsx = null\n    }\n\n    return options.createElement(\n      wrapper,\n      { key: 'outer' },\n      jsx\n    ) as React.JSX.Element\n  }\n\n  function attrStringToMap(\n    tag: MarkdownToJSX.HTMLTags,\n    str: string\n  ): React.JSX.IntrinsicAttributes {\n    const attributes = str.match(ATTR_EXTRACTOR_R)\n    if (!attributes) {\n      return null\n    }\n\n    return attributes.reduce(function (map, raw) {\n      const delimiterIdx = raw.indexOf('=')\n\n      if (delimiterIdx !== -1) {\n        const key = normalizeAttributeKey(raw.slice(0, delimiterIdx)).trim()\n        const value = unquote(raw.slice(delimiterIdx + 1).trim())\n\n        const mappedKey = ATTRIBUTE_TO_JSX_PROP_MAP[key] || key\n\n        // bail out, not supported\n        if (mappedKey === 'ref') return map\n\n        const normalizedValue = (map[mappedKey] = attributeValueToJSXPropValue(\n          tag,\n          key,\n          value,\n          options.sanitizer\n        ))\n\n        if (\n          typeof normalizedValue === 'string' &&\n          (HTML_BLOCK_ELEMENT_R.test(normalizedValue) ||\n            HTML_SELF_CLOSING_ELEMENT_R.test(normalizedValue))\n        ) {\n          map[mappedKey] = compile(normalizedValue.trim())\n        }\n      } else if (raw !== 'style') {\n        map[ATTRIBUTE_TO_JSX_PROP_MAP[raw] || raw] = true\n      }\n\n      return map\n    }, {})\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof markdown !== 'string') {\n      throw new Error(`markdown-to-jsx: the first argument must be\n                             a string`)\n    }\n\n    if (\n      Object.prototype.toString.call(options.overrides) !== '[object Object]'\n    ) {\n      throw new Error(`markdown-to-jsx: options.overrides (second argument property) must be\n                             undefined or an object literal with shape:\n                             {\n                                htmltagname: {\n                                    component: string|ReactComponent(optional),\n                                    props: object(optional)\n                                }\n                             }`)\n    }\n  }\n\n  const footnotes: { footnote: string; identifier: string }[] = []\n  const refs: { [key: string]: { target: string; title: string } } = {}\n\n  /**\n   * each rule's react() output function goes through our custom\n   * h() JSX pragma; this allows the override functionality to be\n   * automatically applied\n   */\n  // @ts-ignore\n  const rules: MarkdownToJSX.Rules = {\n    [RuleType.blockQuote]: {\n      match: blockRegex(BLOCKQUOTE_R),\n      order: Priority.HIGH,\n      parse(capture, parse, state) {\n        const [, alert, content] = capture[0]\n          .replace(BLOCKQUOTE_TRIM_LEFT_MULTILINE_R, '')\n          .match(BLOCKQUOTE_ALERT_R)\n\n        return {\n          alert,\n          children: parse(content, state),\n        }\n      },\n      render(node, output, state) {\n        const props = {\n          key: state.key,\n        } as Record<string, unknown>\n\n        if (node.alert) {\n          props.className =\n            'markdown-alert-' +\n            options.slugify(node.alert.toLowerCase(), slugify)\n\n          node.children.unshift({\n            attrs: {},\n            children: [{ type: RuleType.text, text: node.alert }],\n            noInnerParse: true,\n            type: RuleType.htmlBlock,\n            tag: 'header',\n          })\n        }\n\n        return h('blockquote', props, output(node.children, state))\n      },\n    },\n\n    [RuleType.breakLine]: {\n      match: anyScopeRegex(BREAK_LINE_R),\n      order: Priority.HIGH,\n      parse: captureNothing,\n      render(_, __, state) {\n        return <br key={state.key} />\n      },\n    },\n\n    [RuleType.breakThematic]: {\n      match: blockRegex(BREAK_THEMATIC_R),\n      order: Priority.HIGH,\n      parse: captureNothing,\n      render(_, __, state) {\n        return <hr key={state.key} />\n      },\n    },\n\n    [RuleType.codeBlock]: {\n      match: blockRegex(CODE_BLOCK_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          lang: undefined,\n          text: trimEnd(capture[0].replace(/^ {4}/gm, '')).replace(\n            TEXT_UNESCAPE_R,\n            '$1'\n          ),\n        }\n      },\n\n      render(node, output, state) {\n        return (\n          <pre key={state.key}>\n            <code\n              {...node.attrs}\n              className={node.lang ? `lang-${node.lang}` : ''}\n            >\n              {node.text}\n            </code>\n          </pre>\n        )\n      },\n    } as MarkdownToJSX.Rule<{\n      attrs?: ReturnType<typeof attrStringToMap>\n      lang?: string\n      text: string\n    }>,\n\n    [RuleType.codeFenced]: {\n      match: blockRegex(CODE_BLOCK_FENCED_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          // if capture[3] it's additional metadata\n          attrs: attrStringToMap('code', capture[3] || ''),\n          lang: capture[2] || undefined,\n          text: capture[4],\n          type: RuleType.codeBlock,\n        }\n      },\n    },\n\n    [RuleType.codeInline]: {\n      match: simpleInlineRegex(CODE_INLINE_R),\n      order: Priority.LOW,\n      parse(capture /*, parse, state*/) {\n        return {\n          text: capture[2].replace(TEXT_UNESCAPE_R, '$1'),\n        }\n      },\n      render(node, output, state) {\n        return <code key={state.key}>{node.text}</code>\n      },\n    },\n\n    /**\n     * footnotes are emitted at the end of compilation in a special <footer> block\n     */\n    [RuleType.footnote]: {\n      match: blockRegex(FOOTNOTE_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        footnotes.push({\n          footnote: capture[2],\n          identifier: capture[1],\n        })\n\n        return {}\n      },\n      render: renderNothing,\n    },\n\n    [RuleType.footnoteReference]: {\n      match: inlineRegex(FOOTNOTE_REFERENCE_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse*/) {\n        return {\n          target: `#${options.slugify(capture[1], slugify)}`,\n          text: capture[1],\n        }\n      },\n      render(node, output, state) {\n        return (\n          <a key={state.key} href={options.sanitizer(node.target, 'a', 'href')}>\n            <sup key={state.key}>{node.text}</sup>\n          </a>\n        )\n      },\n    } as MarkdownToJSX.Rule<{ target: string; text: string }>,\n\n    [RuleType.gfmTask]: {\n      match: inlineRegex(GFM_TASK_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        return {\n          completed: capture[1].toLowerCase() === 'x',\n        }\n      },\n      render(node, output, state) {\n        return (\n          <input\n            checked={node.completed}\n            key={state.key}\n            readOnly\n            type=\"checkbox\"\n          />\n        )\n      },\n    } as MarkdownToJSX.Rule<{ completed: boolean }>,\n\n    [RuleType.heading]: {\n      match: blockRegex(\n        options.enforceAtxHeadings ? HEADING_ATX_COMPLIANT_R : HEADING_R\n      ),\n      order: Priority.HIGH,\n      parse(capture, parse, state) {\n        return {\n          children: parseInline(parse, capture[2], state),\n          id: options.slugify(capture[2], slugify),\n          level: capture[1].length as MarkdownToJSX.HeadingNode['level'],\n        }\n      },\n      render(node, output, state) {\n        return h(\n          `h${node.level}`,\n          { id: node.id, key: state.key },\n          output(node.children, state)\n        )\n      },\n    },\n\n    [RuleType.headingSetext]: {\n      match: blockRegex(HEADING_SETEXT_R),\n      order: Priority.MAX,\n      parse(capture, parse, state) {\n        return {\n          children: parseInline(parse, capture[1], state),\n          level: capture[2] === '=' ? 1 : 2,\n          type: RuleType.heading,\n        }\n      },\n    },\n\n    [RuleType.htmlBlock]: {\n      /**\n       * find the first matching end tag and process the interior\n       */\n      match: anyScopeRegex(HTML_BLOCK_ELEMENT_R),\n      order: Priority.HIGH,\n      parse(capture, parse, state) {\n        const [, whitespace] = capture[3].match(HTML_LEFT_TRIM_AMOUNT_R)\n\n        const trimmer = new RegExp(`^${whitespace}`, 'gm')\n        const trimmed = capture[3].replace(trimmer, '')\n\n        const parseFunc = containsBlockSyntax(trimmed)\n          ? parseBlock\n          : parseInline\n\n        const tagName = capture[1].toLowerCase() as MarkdownToJSX.HTMLTags\n        const noInnerParse =\n          DO_NOT_PROCESS_HTML_ELEMENTS.indexOf(tagName) !== -1\n\n        const tag = (\n          noInnerParse ? tagName : capture[1]\n        ).trim() as MarkdownToJSX.HTMLTags\n\n        const ast = {\n          attrs: attrStringToMap(tag, capture[2]),\n          noInnerParse: noInnerParse,\n          tag,\n        } as {\n          attrs: ReturnType<typeof attrStringToMap>\n          children?: ReturnType<MarkdownToJSX.NestedParser> | undefined\n          noInnerParse: Boolean\n          tag: MarkdownToJSX.HTMLTags\n          text?: string | undefined\n        }\n\n        state.inAnchor = state.inAnchor || tagName === 'a'\n\n        if (noInnerParse) {\n          ast.text = capture[3]\n        } else {\n          ast.children = parseFunc(parse, trimmed, state)\n        }\n\n        /**\n         * if another html block is detected within, parse as block,\n         * otherwise parse as inline to pick up any further markdown\n         */\n        state.inAnchor = false\n\n        return ast\n      },\n      render(node, output, state) {\n        return (\n          <node.tag key={state.key} {...node.attrs}>\n            {node.text || (node.children ? output(node.children, state) : '')}\n          </node.tag>\n        )\n      },\n    },\n\n    [RuleType.htmlSelfClosing]: {\n      /**\n       * find the first matching end tag and process the interior\n       */\n      match: anyScopeRegex(HTML_SELF_CLOSING_ELEMENT_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        const tag = capture[1].trim() as MarkdownToJSX.HTMLTags\n        return {\n          attrs: attrStringToMap(tag, capture[2] || ''),\n          tag,\n        }\n      },\n      render(node, output, state) {\n        return <node.tag {...node.attrs} key={state.key} />\n      },\n    },\n\n    [RuleType.htmlComment]: {\n      match: anyScopeRegex(HTML_COMMENT_R),\n      order: Priority.HIGH,\n      parse() {\n        return {}\n      },\n      render: renderNothing,\n    },\n\n    [RuleType.image]: {\n      match: simpleInlineRegex(IMAGE_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        return {\n          alt: capture[1],\n          target: unescapeUrl(capture[2]),\n          title: capture[3],\n        }\n      },\n      render(node, output, state) {\n        return (\n          <img\n            key={state.key}\n            alt={node.alt || undefined}\n            title={node.title || undefined}\n            src={options.sanitizer(node.target, 'img', 'src')}\n          />\n        )\n      },\n    } as MarkdownToJSX.Rule<{\n      alt?: string\n      target: string\n      title?: string\n    }>,\n\n    [RuleType.link]: {\n      match: inlineRegex(LINK_R),\n      order: Priority.LOW,\n      parse(capture, parse, state) {\n        return {\n          children: parseSimpleInline(parse, capture[1], state),\n          target: unescapeUrl(capture[2]),\n          title: capture[3],\n        }\n      },\n      render(node, output, state) {\n        return (\n          <a\n            key={state.key}\n            href={options.sanitizer(node.target, 'a', 'href')}\n            title={node.title}\n          >\n            {output(node.children, state)}\n          </a>\n        )\n      },\n    },\n\n    // https://daringfireball.net/projects/markdown/syntax#autolink\n    [RuleType.linkAngleBraceStyleDetector]: {\n      match: inlineRegex(LINK_AUTOLINK_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          children: [\n            {\n              text: capture[1],\n              type: RuleType.text,\n            },\n          ],\n          target: capture[1],\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.linkBareUrlDetector]: {\n      match: allowInline((source, state) => {\n        if (state.inAnchor || options.disableAutoLink) {\n          return null\n        }\n\n        return inlineRegex(LINK_AUTOLINK_BARE_URL_R)(source, state)\n      }),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        return {\n          children: [\n            {\n              text: capture[1],\n              type: RuleType.text,\n            },\n          ],\n          target: capture[1],\n          title: undefined,\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.linkMailtoDetector]: {\n      match: inlineRegex(LINK_AUTOLINK_MAILTO_R),\n      order: Priority.MAX,\n      parse(capture /*, parse, state*/) {\n        let address = capture[1]\n        let target = capture[1]\n\n        // Check for a `mailto:` already existing in the link:\n        if (!AUTOLINK_MAILTO_CHECK_R.test(target)) {\n          target = 'mailto:' + target\n        }\n\n        return {\n          children: [\n            {\n              text: address.replace('mailto:', ''),\n              type: RuleType.text,\n            },\n          ],\n          target: target,\n          type: RuleType.link,\n        }\n      },\n    },\n\n    [RuleType.orderedList]: generateListRule(\n      h,\n      ORDERED\n    ) as MarkdownToJSX.Rule<MarkdownToJSX.OrderedListNode>,\n\n    [RuleType.unorderedList]: generateListRule(\n      h,\n      UNORDERED\n    ) as MarkdownToJSX.Rule<MarkdownToJSX.UnorderedListNode>,\n\n    [RuleType.newlineCoalescer]: {\n      match: blockRegex(CONSECUTIVE_NEWLINE_R),\n      order: Priority.LOW,\n      parse: captureNothing,\n      render(/*node, output, state*/) {\n        return '\\n'\n      },\n    },\n\n    [RuleType.paragraph]: {\n      match: allowInline(matchParagraph),\n      order: Priority.LOW,\n      parse: parseCaptureInline,\n      render(node, output, state) {\n        return <p key={state.key}>{output(node.children, state)}</p>\n      },\n    } as MarkdownToJSX.Rule<ReturnType<typeof parseCaptureInline>>,\n\n    [RuleType.ref]: {\n      match: inlineRegex(REFERENCE_IMAGE_OR_LINK),\n      order: Priority.MAX,\n      parse(capture /*, parse*/) {\n        refs[capture[1]] = {\n          target: capture[2],\n          title: capture[4],\n        }\n\n        return {}\n      },\n      render: renderNothing,\n    },\n\n    [RuleType.refImage]: {\n      match: simpleInlineRegex(REFERENCE_IMAGE_R),\n      order: Priority.MAX,\n      parse(capture) {\n        return {\n          alt: capture[1] || undefined,\n          ref: capture[2],\n        }\n      },\n      render(node, output, state) {\n        return refs[node.ref] ? (\n          <img\n            key={state.key}\n            alt={node.alt}\n            src={options.sanitizer(refs[node.ref].target, 'img', 'src')}\n            title={refs[node.ref].title}\n          />\n        ) : null\n      },\n    } as MarkdownToJSX.Rule<{ alt?: string; ref: string }>,\n\n    [RuleType.refLink]: {\n      match: inlineRegex(REFERENCE_LINK_R),\n      order: Priority.MAX,\n      parse(capture, parse, state) {\n        return {\n          children: parse(capture[1], state),\n          fallbackChildren: capture[0],\n          ref: capture[2],\n        }\n      },\n      render(node, output, state) {\n        return refs[node.ref] ? (\n          <a\n            key={state.key}\n            href={options.sanitizer(refs[node.ref].target, 'a', 'href')}\n            title={refs[node.ref].title}\n          >\n            {output(node.children, state)}\n          </a>\n        ) : (\n          <span key={state.key}>{node.fallbackChildren}</span>\n        )\n      },\n    },\n\n    [RuleType.table]: {\n      match: blockRegex(NP_TABLE_R),\n      order: Priority.HIGH,\n      parse: parseTable,\n      render(node, output, state) {\n        const table = node as MarkdownToJSX.TableNode\n        return (\n          <table key={state.key}>\n            <thead>\n              <tr>\n                {table.header.map(function generateHeaderCell(content, i) {\n                  return (\n                    <th key={i} style={getTableStyle(table, i)}>\n                      {output(content, state)}\n                    </th>\n                  )\n                })}\n              </tr>\n            </thead>\n\n            <tbody>\n              {table.cells.map(function generateTableRow(row, i) {\n                return (\n                  <tr key={i}>\n                    {row.map(function generateTableCell(content, c) {\n                      return (\n                        <td key={c} style={getTableStyle(table, c)}>\n                          {output(content, state)}\n                        </td>\n                      )\n                    })}\n                  </tr>\n                )\n              })}\n            </tbody>\n          </table>\n        )\n      },\n    },\n\n    [RuleType.text]: {\n      // Here we look for anything followed by non-symbols,\n      // double newlines, or double-space-newlines\n      // We break on any symbol characters so that this grammar\n      // is easy to extend without needing to modify this regex\n      match: anyScopeRegex(TEXT_PLAIN_R),\n      order: Priority.MIN,\n      parse(capture /*, parse, state*/) {\n        return {\n          text: capture[0]\n            // nbsp -> unicode equivalent for named chars\n            .replace(HTML_CHAR_CODE_R, (full, inner) => {\n              return options.namedCodesToUnicode[inner]\n                ? options.namedCodesToUnicode[inner]\n                : full\n            }),\n        }\n      },\n      render(node /*, output, state*/) {\n        return node.text\n      },\n    },\n\n    [RuleType.textBolded]: {\n      match: simpleInlineRegex(TEXT_BOLD_R),\n      order: Priority.MED,\n      parse(capture, parse, state) {\n        return {\n          // capture[1] -> the syntax control character\n          // capture[2] -> inner content\n          children: parse(capture[2], state),\n        }\n      },\n      render(node, output, state) {\n        return <strong key={state.key}>{output(node.children, state)}</strong>\n      },\n    },\n\n    [RuleType.textEmphasized]: {\n      match: simpleInlineRegex(TEXT_EMPHASIZED_R),\n      order: Priority.LOW,\n      parse(capture, parse, state) {\n        return {\n          // capture[1] -> opening * or _\n          // capture[2] -> inner content\n          children: parse(capture[2], state),\n        }\n      },\n      render(node, output, state) {\n        return <em key={state.key}>{output(node.children, state)}</em>\n      },\n    },\n\n    [RuleType.textEscaped]: {\n      // We don't allow escaping numbers, letters, or spaces here so that\n      // backslashes used in plain text still get rendered. But allowing\n      // escaping anything else provides a very flexible escape mechanism,\n      // regardless of how this grammar is extended.\n      match: simpleInlineRegex(TEXT_ESCAPED_R),\n      order: Priority.HIGH,\n      parse(capture /*, parse, state*/) {\n        return {\n          text: capture[1],\n          type: RuleType.text,\n        }\n      },\n    },\n\n    [RuleType.textMarked]: {\n      match: simpleInlineRegex(TEXT_MARKED_R),\n      order: Priority.LOW,\n      parse: parseCaptureInline,\n      render(node, output, state) {\n        return <mark key={state.key}>{output(node.children, state)}</mark>\n      },\n    },\n\n    [RuleType.textStrikethroughed]: {\n      match: simpleInlineRegex(TEXT_STRIKETHROUGHED_R),\n      order: Priority.LOW,\n      parse: parseCaptureInline,\n      render(node, output, state) {\n        return <del key={state.key}>{output(node.children, state)}</del>\n      },\n    },\n  }\n\n  // Object.keys(rules).forEach(key => {\n  //   let { match: match, parse: parse } = rules[key]\n\n  //   // rules[key].match = (...args) => {\n  //   //   const start = performance.now()\n  //   //   const result = match(...args)\n  //   //   const delta = performance.now() - start\n\n  //   //   if (delta > 5)\n  //   //     console.warn(\n  //   //       `Slow match for ${key}: ${delta.toFixed(3)}ms, input: ${args[0]}`\n  //   //     )\n\n  //   //   return result\n  //   // }\n\n  //   rules[key].parse = (...args) => {\n  //     const start = performance.now()\n  //     const result = parse(...args)\n  //     const delta = performance.now() - start\n\n  //     console[delta > 5 ? 'warn' : 'log'](\n  //       `${key}:parse`,\n  //       `${delta.toFixed(3)}ms`,\n  //       args[0]\n  //     )\n\n  //     return result\n  //   }\n  // })\n\n  if (options.disableParsingRawHTML === true) {\n    delete rules[RuleType.htmlBlock]\n    delete rules[RuleType.htmlSelfClosing]\n  }\n\n  const parser = parserFor(rules)\n  const emitter: Function = reactFor(createRenderer(rules, options.renderRule))\n\n  const jsx = compile(markdown)\n\n  if (footnotes.length) {\n    return (\n      <div>\n        {jsx}\n        <footer key=\"footer\">\n          {footnotes.map(function createFootnote(def) {\n            return (\n              <div\n                id={options.slugify(def.identifier, slugify)}\n                key={def.identifier}\n              >\n                {def.identifier}\n                {emitter(parser(def.footnote, { inline: true }))}\n              </div>\n            )\n          })}\n        </footer>\n      </div>\n    )\n  }\n\n  return jsx\n}\n\n/**\n * A simple HOC for easy React use. Feed the markdown content as a direct child\n * and the rest is taken care of automatically.\n */\nconst Markdown: React.FC<\n  Omit<React.HTMLAttributes<Element>, 'children'> & {\n    children: string\n    options?: MarkdownToJSX.Options\n  }\n> = ({ children = '', options, ...props }) => {\n  if (process.env.NODE_ENV !== 'production' && typeof children !== 'string') {\n    console.error(\n      'markdown-to-jsx: <Markdown> component only accepts a single string as a child, received:',\n      children\n    )\n  }\n\n  return React.cloneElement(\n    compiler(children, options),\n    props as React.JSX.IntrinsicAttributes\n  )\n}\n\nexport namespace MarkdownToJSX {\n  /**\n   * RequireAtLeastOne<{ ... }> <- only requires at least one key\n   */\n  type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<\n    T,\n    Exclude<keyof T, Keys>\n  > &\n    {\n      [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>\n    }[Keys]\n\n  export type CreateElement = typeof React.createElement\n\n  export type HTMLTags = keyof React.JSX.IntrinsicElements\n\n  export type State = {\n    /** true if the current content is inside anchor link grammar */\n    inAnchor?: boolean\n    /** true if parsing in an inline context (subset of rules around formatting and links) */\n    inline?: boolean\n    /** true if in a table */\n    inTable?: boolean\n    /** use this for the `key` prop */\n    key?: React.Key\n    /** true if in a list */\n    list?: boolean\n    /** used for lookbacks */\n    prevCapture?: string\n    /** true if parsing in inline context w/o links */\n    simple?: boolean\n  }\n\n  export interface BlockQuoteNode {\n    alert?: string\n    children: MarkdownToJSX.ParserResult[]\n    type: typeof RuleType.blockQuote\n  }\n\n  export interface BreakLineNode {\n    type: typeof RuleType.breakLine\n  }\n\n  export interface BreakThematicNode {\n    type: typeof RuleType.breakThematic\n  }\n\n  export interface CodeBlockNode {\n    type: typeof RuleType.codeBlock\n    attrs?: React.JSX.IntrinsicAttributes\n    lang?: string\n    text: string\n  }\n\n  export interface CodeFencedNode {\n    type: typeof RuleType.codeFenced\n  }\n\n  export interface CodeInlineNode {\n    type: typeof RuleType.codeInline\n    text: string\n  }\n\n  export interface FootnoteNode {\n    type: typeof RuleType.footnote\n  }\n\n  export interface FootnoteReferenceNode {\n    type: typeof RuleType.footnoteReference\n    target: string\n    text: string\n  }\n\n  export interface GFMTaskNode {\n    type: typeof RuleType.gfmTask\n    completed: boolean\n  }\n\n  export interface HeadingNode {\n    type: typeof RuleType.heading\n    children: MarkdownToJSX.ParserResult[]\n    id: string\n    level: 1 | 2 | 3 | 4 | 5 | 6\n  }\n\n  export interface HeadingSetextNode {\n    type: typeof RuleType.headingSetext\n  }\n\n  export interface HTMLCommentNode {\n    type: typeof RuleType.htmlComment\n  }\n\n  export interface ImageNode {\n    type: typeof RuleType.image\n    alt?: string\n    target: string\n    title?: string\n  }\n\n  export interface LinkNode {\n    type: typeof RuleType.link\n    children: MarkdownToJSX.ParserResult[]\n    target: string\n    title?: string\n  }\n\n  export interface LinkAngleBraceNode {\n    type: typeof RuleType.linkAngleBraceStyleDetector\n  }\n\n  export interface LinkBareURLNode {\n    type: typeof RuleType.linkBareUrlDetector\n  }\n\n  export interface LinkMailtoNode {\n    type: typeof RuleType.linkMailtoDetector\n  }\n\n  export interface OrderedListNode {\n    type: typeof RuleType.orderedList\n    items: MarkdownToJSX.ParserResult[][]\n    ordered: true\n    start?: number\n  }\n\n  export interface UnorderedListNode {\n    type: typeof RuleType.unorderedList\n    items: MarkdownToJSX.ParserResult[][]\n    ordered: false\n  }\n\n  export interface NewlineNode {\n    type: typeof RuleType.newlineCoalescer\n  }\n\n  export interface ParagraphNode {\n    type: typeof RuleType.paragraph\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface ReferenceNode {\n    type: typeof RuleType.ref\n  }\n\n  export interface ReferenceImageNode {\n    type: typeof RuleType.refImage\n    alt?: string\n    ref: string\n  }\n\n  export interface ReferenceLinkNode {\n    type: typeof RuleType.refLink\n    children: MarkdownToJSX.ParserResult[]\n    fallbackChildren: string\n    ref: string\n  }\n\n  export interface TableNode {\n    type: typeof RuleType.table\n    /**\n     * alignment for each table column\n     */\n    align: ('left' | 'right' | 'center')[]\n    cells: MarkdownToJSX.ParserResult[][][]\n    header: MarkdownToJSX.ParserResult[][]\n  }\n\n  export interface TableSeparatorNode {\n    type: typeof RuleType.tableSeparator\n  }\n\n  export interface TextNode {\n    type: typeof RuleType.text\n    text: string\n  }\n\n  export interface BoldTextNode {\n    type: typeof RuleType.textBolded\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface ItalicTextNode {\n    type: typeof RuleType.textEmphasized\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface EscapedTextNode {\n    type: typeof RuleType.textEscaped\n  }\n\n  export interface MarkedTextNode {\n    type: typeof RuleType.textMarked\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface StrikethroughTextNode {\n    type: typeof RuleType.textStrikethroughed\n    children: MarkdownToJSX.ParserResult[]\n  }\n\n  export interface HTMLNode {\n    type: typeof RuleType.htmlBlock\n    attrs: React.JSX.IntrinsicAttributes\n    children?: ReturnType<MarkdownToJSX.NestedParser> | undefined\n    noInnerParse: Boolean\n    tag: MarkdownToJSX.HTMLTags\n    text?: string | undefined\n  }\n\n  export interface HTMLSelfClosingNode {\n    type: typeof RuleType.htmlSelfClosing\n    attrs: React.JSX.IntrinsicAttributes\n    tag: string\n  }\n\n  export type ParserResult =\n    | BlockQuoteNode\n    | BreakLineNode\n    | BreakThematicNode\n    | CodeBlockNode\n    | CodeFencedNode\n    | CodeInlineNode\n    | FootnoteNode\n    | FootnoteReferenceNode\n    | GFMTaskNode\n    | HeadingNode\n    | HeadingSetextNode\n    | HTMLCommentNode\n    | ImageNode\n    | LinkNode\n    | LinkAngleBraceNode\n    | LinkBareURLNode\n    | LinkMailtoNode\n    | OrderedListNode\n    | UnorderedListNode\n    | NewlineNode\n    | ParagraphNode\n    | ReferenceNode\n    | ReferenceImageNode\n    | ReferenceLinkNode\n    | TableNode\n    | TableSeparatorNode\n    | TextNode\n    | BoldTextNode\n    | ItalicTextNode\n    | EscapedTextNode\n    | MarkedTextNode\n    | StrikethroughTextNode\n    | HTMLNode\n    | HTMLSelfClosingNode\n\n  export type NestedParser = (\n    input: string,\n    state?: MarkdownToJSX.State\n  ) => MarkdownToJSX.ParserResult[]\n\n  export type Parser<ParserOutput> = (\n    capture: RegExpMatchArray,\n    nestedParse: NestedParser,\n    state?: MarkdownToJSX.State\n  ) => ParserOutput\n\n  export type RuleOutput = (\n    ast: MarkdownToJSX.ParserResult | MarkdownToJSX.ParserResult[],\n    state: MarkdownToJSX.State\n  ) => React.JSX.Element\n\n  export type Rule<ParserOutput = MarkdownToJSX.ParserResult> = {\n    match: (\n      source: string,\n      state: MarkdownToJSX.State,\n      prevCapturedString?: string\n    ) => RegExpMatchArray\n    order: Priority\n    parse: MarkdownToJSX.Parser<Omit<ParserOutput, 'type'>>\n    render?: (\n      node: ParserOutput,\n      /**\n       * Continue rendering AST nodes if applicable.\n       */\n      render: RuleOutput,\n      state?: MarkdownToJSX.State\n    ) => React.ReactNode\n  }\n\n  export type Rules = {\n    [K in ParserResult['type']]: K extends typeof RuleType.table\n      ? Rule<Extract<ParserResult, { type: K | typeof RuleType.paragraph }>>\n      : Rule<Extract<ParserResult, { type: K }>>\n  }\n\n  export type Override =\n    | RequireAtLeastOne<{\n        component: React.ElementType\n        props: Object\n      }>\n    | React.ElementType\n\n  export type Overrides = {\n    [tag in HTMLTags]?: Override\n  } & {\n    [customComponent: string]: Override\n  }\n\n  export type Options = Partial<{\n    /**\n     * Ultimate control over the output of all rendered JSX.\n     */\n    createElement: (\n      tag: Parameters<CreateElement>[0],\n      props: React.JSX.IntrinsicAttributes,\n      ...children: React.ReactNode[]\n    ) => React.ReactNode\n\n    /**\n     * The library automatically generates an anchor tag for bare URLs included in the markdown\n     * document, but this behavior can be disabled if desired.\n     */\n    disableAutoLink: boolean\n\n    /**\n     * Disable the compiler's best-effort transcription of provided raw HTML\n     * into JSX-equivalent. This is the functionality that prevents the need to\n     * use `dangerouslySetInnerHTML` in React.\n     */\n    disableParsingRawHTML: boolean\n\n    /**\n     * Forces the compiler to have space between hash sign and the header text which\n     * is explicitly stated in the most of the markdown specs.\n     * https://github.github.com/gfm/#atx-heading\n     * `The opening sequence of # characters must be followed by a space or by the end of line.`\n     */\n    enforceAtxHeadings: boolean\n\n    /**\n     * Forces the compiler to always output content with a block-level wrapper\n     * (`<p>` or any block-level syntax your markdown already contains.)\n     */\n    forceBlock: boolean\n\n    /**\n     * Forces the compiler to always output content with an inline wrapper (`<span>`)\n     */\n    forceInline: boolean\n\n    /**\n     * Forces the compiler to wrap results, even if there is only a single\n     * child or no children.\n     */\n    forceWrapper: boolean\n\n    /**\n     * Supply additional HTML entity: unicode replacement mappings.\n     *\n     * Pass only the inner part of the entity as the key,\n     * e.g. `&le;` -> `{ \"le\": \"\\u2264\" }`\n     *\n     * By default\n     * the following entities are replaced with their unicode equivalents:\n     *\n     * ```\n     * &amp;\n     * &apos;\n     * &gt;\n     * &lt;\n     * &nbsp;\n     * &quot;\n     * ```\n     */\n    namedCodesToUnicode: {\n      [key: string]: string\n    }\n\n    /**\n     * Selectively control the output of particular HTML tags as they would be\n     * emitted by the compiler.\n     */\n    overrides: Overrides\n\n    /**\n     * Allows for full control over rendering of particular rules.\n     * For example, to implement a LaTeX renderer such as `react-katex`:\n     *\n     * ```\n     * renderRule(next, node, renderChildren, state) {\n     *   if (node.type === RuleType.codeBlock && node.lang === 'latex') {\n     *     return (\n     *       <TeX as=\"div\" key={state.key}>\n     *         {String.raw`${node.text}`}\n     *       </TeX>\n     *     )\n     *   }\n     *\n     *   return next();\n     * }\n     * ```\n     *\n     * Thar be dragons obviously, but you can do a lot with this\n     * (have fun!) To see how things work internally, check the `render`\n     * method in source for a particular rule.\n     */\n    renderRule: (\n      /** Resume normal processing, call this function as a fallback if you are not returning custom JSX. */\n      next: () => React.ReactNode,\n      /** the current AST node, use `RuleType` against `node.type` for identification */\n      node: ParserResult,\n      /** use as `renderChildren(node.children)` for block nodes */\n      renderChildren: RuleOutput,\n      /** contains `key` which should be supplied to the topmost JSX element */\n      state: State\n    ) => React.ReactNode\n\n    /**\n     * Override the built-in sanitizer function for URLs, etc if desired. The built-in version is available as a library export called `sanitizer`.\n     */\n    sanitizer: (\n      value: string,\n      tag: HTMLTags,\n      attribute: string\n    ) => string | null\n\n    /**\n     * Override normalization of non-URI-safe characters for use in generating\n     * HTML IDs for anchor linking purposes.\n     */\n    slugify: (input: string, defaultFn: (input: string) => string) => string\n\n    /**\n     * Declare the type of the wrapper to be used when there are multiple\n     * children to render. Set to `null` to get an array of children back\n     * without any wrapper, or use `React.Fragment` to get a React element\n     * that won't show up in the DOM.\n     */\n    wrapper: React.ElementType | null\n  }>\n}\n\nexport default Markdown\n"], "names": ["Priority", "RuleType", "blockQuote", "breakLine", "breakThematic", "codeBlock", "codeFenced", "codeInline", "footnote", "footnoteReference", "gfmTask", "heading", "headingSetext", "htmlBlock", "htmlComment", "htmlSelfClosing", "image", "link", "linkAngleBraceStyleDetector", "linkBareUrlDetector", "linkMailtoDetector", "new<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderedList", "paragraph", "ref", "refImage", "refLink", "table", "tableSeparator", "text", "textBolded", "textEmphasized", "textEscaped", "textMarked", "textStrikethroughed", "unorderedList", "ATTRIBUTE_TO_JSX_PROP_MAP", "reduce", "obj", "x", "toLowerCase", "class", "for", "namedCodesToUnicode", "amp", "apos", "gt", "lt", "nbsp", "quot", "DO_NOT_PROCESS_HTML_ELEMENTS", "ATTRIBUTES_TO_SANITIZE", "ATTR_EXTRACTOR_R", "AUTOLINK_MAILTO_CHECK_R", "BLOCK_END_R", "BLOCKQUOTE_R", "BLOCKQUOTE_TRIM_LEFT_MULTILINE_R", "BLOCKQUOTE_ALERT_R", "BREAK_LINE_R", "BREAK_THEMATIC_R", "CODE_BLOCK_FENCED_R", "CODE_BLOCK_R", "CODE_INLINE_R", "CONSECUTIVE_NEWLINE_R", "CR_NEWLINE_R", "FOOTNOTE_R", "FOOTNOTE_REFERENCE_R", "FORMFEED_R", "FRONT_MATTER_R", "GFM_TASK_R", "HEADING_R", "HEADING_ATX_COMPLIANT_R", "HEADING_SETEXT_R", "HTML_BLOCK_ELEMENT_R", "HTML_CHAR_CODE_R", "HTML_COMMENT_R", "HTML_CUSTOM_ATTR_R", "HTML_SELF_CLOSING_ELEMENT_R", "INTERPOLATION_R", "LINK_AUTOLINK_BARE_URL_R", "LINK_AUTOLINK_MAILTO_R", "LINK_AUTOLINK_R", "CAPTURE_LETTER_AFTER_HYPHEN", "NP_TABLE_R", "REFERENCE_IMAGE_OR_LINK", "REFERENCE_IMAGE_R", "REFERENCE_LINK_R", "SHOULD_RENDER_AS_BLOCK_R", "TAB_R", "TABLE_TRIM_PIPES", "TABLE_CENTER_ALIGN", "TABLE_LEFT_ALIGN", "TABLE_RIGHT_ALIGN", "INLINE_SKIP_R", "TEXT_BOLD_R", "RegExp", "TEXT_EMPHASIZED_R", "TEXT_MARKED_R", "TEXT_STRIKETHROUGHED_R", "TEXT_ESCAPED_R", "TEXT_UNESCAPE_R", "TEXT_PLAIN_R", "TRIM_STARTING_NEWLINES", "HTML_LEFT_TRIM_AMOUNT_R", "UNESCAPE_URL_R", "LIST_LOOKBEHIND_R", "ORDERED_LIST_BULLET", "UNORDERED_LIST_BULLET", "generateListItemPrefix", "type", "ORDERED_LIST_ITEM_PREFIX", "UNORDERED_LIST_ITEM_PREFIX", "generateListItemPrefixRegex", "ORDERED_LIST_ITEM_PREFIX_R", "UNORDERED_LIST_ITEM_PREFIX_R", "generateListItemRegex", "ORDERED_LIST_ITEM_R", "UNORDERED_LIST_ITEM_R", "generateListRegex", "bullet", "ORDERED_LIST_R", "UNORDERED_LIST_R", "generateListRule", "h", "ordered", "LIST_R", "LIST_ITEM_R", "LIST_ITEM_PREFIX_R", "match", "allowInline", "source", "state", "isStartOfLine", "exec", "prevCapture", "list", "inline", "simple", "order", "parse", "capture", "start", "undefined", "items", "replace", "lastItemWasAParagraph", "map", "item", "i", "space", "length", "spaceRegex", "content", "isLastItem", "thisItemIsAParagraph", "indexOf", "adjustedContent", "oldStateInline", "oldStateList", "trimEnd", "result", "render", "node", "output", "key", "LINK_R", "IMAGE_R", "NON_PARAGRAPH_BLOCK_SYNTAXES", "BLOCK_SYNTAXES", "concat", "str", "end", "slice", "slugify", "parseTableAlignCapture", "alignCapture", "test", "parseTableRow", "tableOutput", "prevInTable", "inTable", "cells", "acc", "flush", "cell", "push", "apply", "trim", "split", "filter", "Boolean", "for<PERSON>ach", "fragment", "arr", "parseTable", "align", "rowText", "parseTableCells", "header", "children", "getTableStyle", "colIndex", "textAlign", "fn", "inlineRegex", "regex", "simpleInlineRegex", "blockRegex", "anyScopeRegex", "matchParagraph", "every", "line", "some", "captured", "SANITIZE_R", "sanitizer", "input", "decoded", "decodeURIComponent", "e", "unescapeUrl", "rawUrlString", "parseInline", "isCurrentlyInline", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseSimpleInline", "parseBlock", "parseCaptureInline", "captureNothing", "renderNothing", "cx", "call", "arguments", "join", "get", "src", "path", "fb", "ptr", "frags", "shift", "getTag", "tag", "overrides", "override", "compiler", "markdown", "options", "props", "overrideProps", "_options", "createElement", "_extends", "className", "compile", "forceInline", "forceBlock", "emitter", "parser", "pop", "wrapper", "jsx", "forceWrapper", "attrStringToMap", "attributes", "raw", "delimiterIdx", "_", "letter", "toUpperCase", "normalizeAttributeKey", "value", "first", "unquote", "<PERSON><PERSON><PERSON>", "normalizedValue", "sanitizeUrlFn", "styleString", "styles", "buffer", "inUrl", "inQuotes", "quoteChar", "char", "endsWith", "declaration", "colonIndex", "parseStyleAttribute", "_ref", "substr", "attributeValueToJSXPropValue", "React", "footnotes", "refs", "rules", "_rules", "_capture$0$replace$ma", "alert", "unshift", "attrs", "noInnerParse", "__", "lang", "identifier", "target", "href", "completed", "checked", "readOnly", "enforceAtxHeadings", "id", "level", "_capture$3$match", "trimmer", "trimmed", "parseFunc", "r", "tagName", "ast", "inAnchor", "alt", "title", "disableAutoLink", "address", "fallback<PERSON><PERSON><PERSON><PERSON>", "style", "row", "c", "full", "inner", "disableParsingRawHTML", "ruleList", "Object", "keys", "nestedParse", "rule", "parsed", "ruleType", "currCaptureString", "substring", "sort", "typeA", "typeB", "orderA", "orderB", "normalizeWhitespace", "parser<PERSON><PERSON>", "userRender", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "renderRule", "patchedRender", "Array", "isArray", "<PERSON><PERSON><PERSON>", "lastWasString", "nodeOut", "isString", "def", "_ref2", "_ref2$children", "_objectWithoutPropertiesLoose", "_excluded", "cloneElement"], "mappings": "gQA8DWA,2BAhDEC,EAAW,CACtBC,WAAY,IACZC,UAAW,IACXC,cAAe,IACfC,UAAW,IACXC,WAAY,IACZC,WAAY,IACZC,SAAU,IACVC,kBAAmB,IACnBC,QAAS,IACTC,QAAS,IACTC,cAAe,KAEfC,UAAW,KACXC,YAAa,KAEbC,gBAAiB,KACjBC,MAAO,KACPC,KAAM,KAENC,4BAA6B,KAE7BC,oBAAqB,KAErBC,mBAAoB,KACpBC,iBAAkB,KAClBC,YAAa,KACbC,UAAW,KACXC,IAAK,KACLC,SAAU,KACVC,QAAS,KACTC,MAAO,KACPC,eAAgB,KAChBC,KAAM,KACNC,WAAY,KACZC,eAAgB,KAChBC,YAAa,KACbC,WAAY,KACZC,oBAAqB,KACrBC,cAAe,OASjB,SAAWnC,GAITA,iBAIAA,mBAIAA,iBAIAA,iBAIAA,gBACD,CArBD,CAAWA,IAAAA,OAwBX,IAAMoC,EAA4B,CAChC,kBACA,oBACA,eACA,YACA,WACA,cACA,cACA,UACA,UACA,UACA,kBACA,cACA,cACA,UACA,aACA,cACA,aACA,iBACA,aACA,cACA,WACA,YACA,YACA,UACA,eACA,cACA,YACA,aACA,YACA,aACA,aACA,WACA,UACA,aACA,SACA,UACA,SACA,WACA,UACAC,OACA,SAACC,EAAKC,GAEJ,OADAD,EAAIC,EAAEC,eAAiBD,EAChBD,CACT,EACA,CAAEG,MAAO,YAAaC,IAAK,YAGvBC,EAAsB,CAC1BC,IAAK,IACLC,KAAM,IACNC,GAAI,IACJC,GAAI,IACJC,KAAM,IACNC,KAAM,KAGFC,EAA+B,CAAC,QAAS,UACzCC,EAAyB,CAC7B,MACA,OACA,OACA,aACA,SACA,UAmCIC,EACJ,+GAIIC,EAA0B,WAC1BC,EAAc,UACdC,EAAe,4BACfC,EAAmC,WACnCC,EAAqB,iCACrBC,EAAe,WACfC,EAAmB,kCACnBC,EACJ,uEACIC,EAAe,kCACfC,EAAgB,iCAChBC,EAAwB,eACxBC,EAAe,SAkCfC,EAAa,sDAEbC,EAAuB,iBACvBC,EAAa,MACbC,EAAiB,mCACjBC,EAAa,kBACbC,EAAY,mDACZC,EACJ,mDACIC,EAAmB,sCAwBnBC,EACJ,wIAEIC,EAAmB,iDAEnBC,EAAiB,uBAKjBC,EAAqB,oCAErBC,EACJ,wEACIC,EAAkB,WAClBC,EAA2B,uCAC3BC,EAAyB,qBACzBC,EAAkB,uBAClBC,EAA8B,cAC9BC,EAAa,8DAEbC,EAA0B,gDAC1BC,EAAoB,+BACpBC,EAAmB,8BACnBC,EAA2B,qCAC3BC,EAAQ,MACRC,EAAmB,iBACnBC,EAAqB,aACrBC,EAAmB,YACnBC,EAAoB,YAOpBC,EACJ,2EAMIC,EAAc,IAAIC,oBAAoBF,mBAKtCG,EAAoB,IAAID,iBAAiBF,gBAKzCI,EAAgB,IAAIF,eAAeF,SAKnCK,GAAyB,IAAIH,eAAeF,SAE5CM,GAAiB,sBACjBC,GAAkB,sBAMlBC,GAAe,iDAEfC,GAAyB,OAEzBC,GAA0B,YAE1BC,GAAiB,aAOjBC,GAAoB,gBAGpBC,GAAsB,cACtBC,GAAwB,YAE9B,SAASC,GAAuBC,GAC9B,MACE,SAZuB,IAatBA,EAAmBH,GAAsBC,IAC1C,KAEJ,CAIA,IAAMG,GAA2BF,GApBN,GAqBrBG,GAA6BH,GApBN,GAsB7B,SAASI,GAA4BH,GACnC,WAAWd,OACT,KAzBuB,IA0BpBc,EAAmBC,GAA2BC,IAErD,CAEA,IAAME,GAA6BD,GA9BR,GA+BrBE,GAA+BF,GA9BR,GAgC7B,SAASG,GAAsBN,GAQ7B,WAAWd,OACT,KA1CuB,IA2CpBc,EACGC,GACAC,IAHN,uBA1CuB,IAgDpBF,EAAmBH,GAAsBC,IAC1C,qBACF,KAEJ,CAEA,IAAMS,GAAsBD,GAtDD,GAuDrBE,GAAwBF,GAtDD,GA0D7B,SAASG,GAAkBT,GACzB,IAAMU,EA5DmB,IA4DVV,EAAmBH,GAAsBC,GAExD,WAAWZ,OACT,SACEwB,EADF,oCAKEA,EACA,OACAA,EAPF,qBAaJ,CAEA,IAAMC,GAAiBF,GA9EI,GA+ErBG,GAAmBH,GA9EI,GAgF7B,SAASI,GACPC,EACAd,GAIA,IAAMe,EAvFmB,IAuFTf,EACVgB,EAASD,EAAUJ,GAAiBC,GACpCK,EAAcF,EAAUR,GAAsBC,GAC9CU,EAAqBH,EACvBX,GACAC,GAEJ,MAAO,CACLc,MAAOC,GAAY,SAAUC,EAAQC,GASnC,IAAMC,EAAgB3B,GAAkB4B,KAAKF,EAAMG,aAGnD,OAAIF,IAFkBD,EAAMI,OAAUJ,EAAMK,SAAWL,EAAMM,QAKpDZ,EAAOQ,KAFdH,EAASE,EAAc,GAAKF,OAMhC,GACAQ,QACAC,eAAMC,EAASD,EAAOR,GACpB,IACMU,EAAQjB,GADCgB,EAAQ,QACWE,EAC5BC,EAAQH,EAAQ,GAGnBI,QAAQ1F,EAAa,MACrB0E,MAAMF,GAELmB,GAAwB,EAgE5B,MAAO,CACLF,MA/DkBA,EAAMG,IAAI,SAAUC,EAAMC,GAE5C,IAAMC,EAAQtB,EAAmBM,KAAKc,GAAM,GAAGG,OAIzCC,EAAa,IAAIxD,OAAO,QAAUsD,EAAQ,IAAK,MAG/CG,EAAUL,EAEbH,QAAQO,EAAY,IAEpBP,QAAQjB,EAAoB,IASzB0B,EAAaL,IAAML,EAAMO,OAAS,EASlCI,GAR8C,IAA7BF,EAAQG,QAAQ,SASlBF,GAAcR,EACnCA,EAAwBS,EAKxB,IAMIE,EANEC,EAAiB1B,EAAMK,OACvBsB,EAAe3B,EAAMI,KAC3BJ,EAAMI,MAAO,EAKTmB,GACFvB,EAAMK,QAAS,EACfoB,EAAkBG,GAAQP,GAAW,SAErCrB,EAAMK,QAAS,EACfoB,EAAkBG,GAAQP,IAG5B,IAAMQ,EAASrB,EAAMiB,EAAiBzB,GAMtC,OAHAA,EAAMK,OAASqB,EACf1B,EAAMI,KAAOuB,EAENE,CACT,GAIEpC,QAASA,EACTiB,MAAOA,EAEX,EACAoB,gBAAOC,EAAMC,EAAQhC,GAGnB,OACER,EAHUuC,EAAKtC,QAAU,KAAO,MAI9BwC,IAAKjC,EAAMiC,IACXvB,MAAOqB,EAAKrD,OAAS5G,EAASqB,YAAc4I,EAAKrB,WAAQC,GAExDoB,EAAKnB,MAAMG,IAAI,SAA0BC,EAAMC,GAC9C,OAAOzB,QAAIyC,IAAKhB,GAAIe,EAAOhB,EAAMhB,GACnC,GAGN,EAEJ,CAEA,IAGMkC,GAAS,IAAItE,OACjB,gJAEIuE,GAAU,0DAEVC,GAA+B,CACnChH,EACAK,EACAC,EACAS,EACAE,EACAD,EACAY,EACAqC,GACAC,IAGI+C,MAAcC,OACfF,IAjSe,yBAmSlB9F,EACAE,EACAE,IAGF,SAASkF,GAAQW,GAEf,IADA,IAAIC,EAAMD,EAAIpB,OACPqB,EAAM,GAAKD,EAAIC,EAAM,IAAM,KAAKA,IACvC,OAAOD,EAAIE,MAAM,EAAGD,EACtB,UAqBgBE,GAAQH,GACtB,OAAOA,EACJ1B,QAAQ,oBAAqB,KAC7BA,QAAQ,QAAS,KACjBA,QAAQ,QAAS,KACjBA,QAAQ,cAAe,KACvBA,QAAQ,cAAe,KACvBA,QAAQ,QAAS,KACjBA,QAAQ,kBAAmB,KAC3BA,QAAQ,cAAe,KACvBA,QAAQ,UAAW,KACnBA,QAAQ,gBAAiB,IACzBA,QAAQ,MAAO,KACfxG,aACL,CAEA,SAASsI,GAAuBC,GAC9B,OAAInF,EAAkBoF,KAAKD,GAClB,QACErF,EAAmBsF,KAAKD,GAC1B,SACEpF,EAAiBqF,KAAKD,GACxB,WAIX,CAEA,SAASE,GACP/C,EACAS,EACAR,EACA+C,GAEA,IAAMC,EAAchD,EAAMiD,QAE1BjD,EAAMiD,SAAU,EAEhB,IAAIC,EAAwC,CAAC,IACzCC,EAAM,GAEV,SAASC,IACP,GAAKD,EAAL,CAEA,IAAME,EAAOH,EAAMA,EAAM/B,OAAS,GAClCkC,EAAKC,KAAKC,MAAMF,EAAM7C,EAAM2C,EAAKnD,IACjCmD,EAAM,GACR,CA4BA,OA1BApD,EACGyD,OAEAC,MAAM,qBACNC,OAAOC,SACPC,QAAQ,SAACC,EAAU5C,EAAG6C,GACG,MAApBD,EAASL,SACXJ,IAEIL,GACQ,IAAN9B,GAAWA,IAAM6C,EAAI3C,OAAS,GAEhC+B,EAAMI,KAAK,IAOjBH,GAAOU,CACT,GAEFT,IAEApD,EAAMiD,QAAUD,EAETE,CACT,CAoBA,SAASa,GACPtD,EACAD,EACAR,GAMAA,EAAMK,QAAS,EACf,IAAM2D,EAAQvD,EAAQ,GAAqBA,EAAQ,GA3B1BI,QAAQvD,EAAkB,IAAImG,MAAM,KAE5C1C,IAAI4B,IAyBoC,GACnDO,EAAQzC,EAAQ,GAvBxB,SACEV,EACAS,EACAR,GAIA,OAFiBD,EAAOyD,OAAOC,MAAM,MAErB1C,IAAI,SAAUkD,GAC5B,OAAOnB,GAAcmB,EAASzD,EAAOR,GAAO,EAC9C,EACF,CAa6BkE,CAAgBzD,EAAQ,GAAID,EAAOR,GAAS,GACjEmE,EAASrB,GAAcrC,EAAQ,GAAID,EAAOR,IAASkD,EAAM/B,QAG/D,OAFAnB,EAAMK,QAAS,EAER6C,EAAM/B,OACT,CACE6C,MAAOA,EACPd,MAAOA,EACPiB,OAAQA,EACRzF,KAAM5G,EAAS0B,OAEjB,CACE4K,SAAUD,EACVzF,KAAM5G,EAASsB,UAEvB,CAEA,SAASiL,GAActC,EAAMuC,GAC3B,OAA+B,MAAxBvC,EAAKiC,MAAMM,GACd,GACA,CACEC,UAAWxC,EAAKiC,MAAMM,GAE9B,CAuPA,SAASxE,GAAqD0E,GAG5D,OAFAA,EAAGnE,OAAS,EAELmE,CACT,CAGA,SAASC,GAAYC,GACnB,OAAO5E,GAAY,SAAeC,EAAQC,GACxC,OAAIA,EAAMK,OACDqE,EAAMxE,KAAKH,OAItB,EACF,CAGA,SAAS4E,GAAkBD,GACzB,OAAO5E,GAAY,SACjBC,EACAC,GAEA,OAAIA,EAAMK,QAAUL,EAAMM,OACjBoE,EAAMxE,KAAKH,OAItB,EACF,CAGA,SAAS6E,GAAWF,GAClB,gBAAsB3E,EAAgBC,GACpC,OAAIA,EAAMK,QAAUL,EAAMM,YAGjBoE,EAAMxE,KAAKH,EAEtB,CACF,CAGA,SAAS8E,GAAcH,GACrB,OAAO5E,GAAY,SAAeC,GAChC,OAAO2E,EAAMxE,KAAKH,EACpB,EACF,CAEA,SAAS+E,GAAe/E,EAAgBC,GACtC,GAAIA,EAAMK,QAAUL,EAAMM,OACxB,YAGF,IAAIT,EAAQ,GAEZE,EAAO0D,MAAM,MAAMsB,MAAM,SAAAC,GAIvB,OAHAA,GAAQ,MAGJ5C,GAA6B6C,KAAK,SAAAP,UAASA,EAAM7B,KAAKmC,EAAK,KAI/DnF,GAASmF,IAEAA,EAAKxB,OAChB,GAEA,IAAM0B,EAAWtD,GAAQ/B,GACzB,MAAgB,IAAZqF,OAMG,CAACrF,GAASqF,EACnB,CAEA,IAAMC,GAAa,kDAEHC,GAAUC,GACxB,IACE,IAAMC,EAAUC,mBAAmBF,GAAOxE,QAAQ,kBAAmB,IAErE,GAAIsE,GAAWtC,KAAKyC,GAQlB,WAcJ,CAZE,MAAOE,GAWP,WACF,CAEA,OAAOH,CACT,CAEA,SAASI,GAAYC,GACnB,OAAOA,EAAa7E,QAAQxC,GAAgB,KAC9C,CAKA,SAASsH,GACPnF,EACA4D,EACApE,GAEA,IAAM4F,EAAoB5F,EAAMK,SAAU,EACpCwF,EAAoB7F,EAAMM,SAAU,EAC1CN,EAAMK,QAAS,EACfL,EAAMM,QAAS,EACf,IAAMuB,EAASrB,EAAM4D,EAAUpE,GAG/B,OAFAA,EAAMK,OAASuF,EACf5F,EAAMM,OAASuF,EACRhE,CACT,CAKA,SAASiE,GACPtF,EACA4D,EACApE,GAEA,IAAM4F,EAAoB5F,EAAMK,SAAU,EACpCwF,EAAoB7F,EAAMM,SAAU,EAC1CN,EAAMK,QAAS,EACfL,EAAMM,QAAS,EACf,IAAMuB,EAASrB,EAAM4D,EAAUpE,GAG/B,OAFAA,EAAMK,OAASuF,EACf5F,EAAMM,OAASuF,EACRhE,CACT,CAEA,SAASkE,GACPvF,EACA4D,EACApE,GAEA,IAAM4F,EAAoB5F,EAAMK,SAAU,EAC1CL,EAAMK,QAAS,EACf,IAAMwB,EAASrB,EAAM4D,EAAUpE,GAE/B,OADAA,EAAMK,OAASuF,EACR/D,CACT,CAEA,IAAMmE,GAED,SAACvF,EAASD,EAAOR,GACpB,MAAO,CACLoE,SAAUuB,GAAYnF,EAAOC,EAAQ,GAAIT,GAE7C,EAEA,SAASiG,KACP,MAAO,EACT,CAEA,SAASC,KACP,WACF,CAwDA,SAASC,KACP,MAAO,GAAA1D,MAAA2D,KAAAC,WAAK3C,OAAOC,SAAS2C,KAAK,IACnC,CAEA,SAASC,GAAIC,EAAaC,EAAcC,GAItC,IAHA,IAAIC,EAAMH,EACJI,EAAQH,EAAKhD,MAAM,KAElBmD,EAAMzF,aAGCR,KAFZgG,EAAMA,EAAIC,EAAM,MAGXA,EAAMC,QAGb,OAAOF,GAAOD,CAChB,CAEA,SAASI,GAAOC,EAAaC,GAC3B,IAAMC,EAAWV,GAAIS,EAAWD,GAEhC,OAAKE,EAEsB,mBAAbA,GACS,iBAAbA,GAAyB,WAAYA,EAC3CA,EACAV,GAAIS,EAAcD,eAAiBA,GALjBA,CAMxB,UAEgBG,GACdC,EACAC,SAaA,SAAS5H,EAEPuH,EACAM,SAMMC,EAAgBf,GAAIa,EAAQJ,UAAcD,WAAa,IAE7D,OAAOQ,EAAAH,GAAQI,cAAajE,MAAAgE,GAC1BT,GAAOC,EAAKK,EAAQJ,WAAUS,KAEzBJ,EACAC,GACHI,UAAWvB,SAAGkB,SAAAA,EAAOK,UAAWJ,EAAcI,iBAAc/G,KAAS2B,UAAAG,MAAA2D,KAAAC,cAI3E,CAEA,SAASsB,EAAQtC,GACfA,EAAQA,EAAMxE,QAAQ5E,EAAgB,IAEtC,IAAIoE,GAAS,EAET+G,EAAQQ,YACVvH,GAAS,EACC+G,EAAQS,aAKlBxH,GAAkD,IAAzCjD,EAAyByF,KAAKwC,IAczC,IAXA,IAAMvB,EAAMgE,GACVC,GACE1H,EACIgF,EACGzD,GAAQyD,GAAOxE,QAAQ1C,GAAwB,WACtD,CACEkC,OAAAA,KAM2B,iBAAxByD,EAAIA,EAAI3C,OAAS,KACvB2C,EAAIA,EAAI3C,OAAS,GAAGqC,QAErBM,EAAIkE,MAGN,GAAwB,OAApBZ,EAAQa,QACV,OAAOnE,EAGT,IACIoE,EADED,EAAUb,EAAQa,UAAY5H,EAAS,OAAS,OAGtD,GAAIyD,EAAI3C,OAAS,GAAKiG,EAAQe,aAC5BD,EAAMpE,UACkB,IAAfA,EAAI3C,OAIb,MAAmB,iBAHnB+G,EAAMpE,EAAI,IAIDtE,UAAMyC,IAAI,SAASiG,GAEnBA,EAITA,EAAM,IACR,CAEA,OAAOd,EAAQI,cACbS,EACA,CAAEhG,IAAK,SACPiG,EAEJ,CAEA,SAASE,EACPrB,EACAxE,GAEA,IAAM8F,EAAa9F,EAAI1C,MAAM5E,GAC7B,OAAKoN,EAIEA,EAAWnO,OAAO,SAAU6G,EAAKuH,GACtC,IAAMC,EAAeD,EAAI9G,QAAQ,KAEjC,IAAsB,IAAlB+G,EAAqB,CACvB,IAAMtG,EA5mBd,SAA+BA,GAS7B,OANqB,IAFDA,EAAIT,QAAQ,MAE4B,OAAlCS,EAAIpC,MAAMpD,KAClCwF,EAAMA,EAAIpB,QAAQ9D,EAA6B,SAAUyL,EAAGC,GAC1D,OAAOA,EAAOC,aAChB,IAGKzG,CACT,CAkmBoB0G,CAAsBL,EAAI7F,MAAM,EAAG8F,IAAe/E,OACxDoF,EAhwBd,SAAiBrG,GACf,IAAMsG,EAAQtG,EAAI,GAClB,OACa,MAAVsG,GAA2B,MAAVA,IAClBtG,EAAIpB,QAAU,GACdoB,EAAIA,EAAIpB,OAAS,KAAO0H,EAEjBtG,EAAIE,MAAM,GAAI,GAEhBF,CACT,CAsvBsBuG,CAAQR,EAAI7F,MAAM8F,EAAe,GAAG/E,QAE5CuF,EAAY9O,EAA0BgI,IAAQA,EAGpD,GAAkB,QAAd8G,EAAqB,OAAOhI,EAEhC,IAAMiI,EAAmBjI,EAAIgI,GAziBrC,SACEhC,EACA9E,EACA2G,EACAK,GAEA,MAAY,UAARhH,EAnEN,SAA6BiH,GAC3B,IAAMC,EAAuB,GACzBC,EAAS,GACTC,GAAQ,EACRC,GAAW,EACXC,EAA4B,GAEhC,IAAKL,EAAa,OAAOC,EAEzB,IAAK,IAAIlI,EAAI,EAAGA,EAAIiI,EAAY/H,OAAQF,IAAK,CAC3C,IAAMuI,EAAON,EAAYjI,GAqBzB,GAlBc,MAATuI,GAAyB,MAATA,GAAkBH,IAChCC,EAGME,IAASD,IAClBD,GAAW,EACXC,EAAY,KAJZD,GAAW,EACXC,EAAYC,IAQH,MAATA,GAAgBJ,EAAOK,SAAS,OAClCJ,GAAQ,EACU,MAATG,GAAgBH,IACzBA,GAAQ,GAIG,MAATG,GAAiBF,GAAaD,EAYhCD,GAAUI,MAZ6B,CACvC,IAAME,EAAcN,EAAO5F,OAC3B,GAAIkG,EAAa,CACf,IAAMC,EAAaD,EAAYlI,QAAQ,KACvC,GAAImI,EAAa,EAAG,CAClB,IAAM1H,EAAMyH,EAAYjH,MAAM,EAAGkH,GAAYnG,OACvCoF,EAAQc,EAAYjH,MAAMkH,EAAa,GAAGnG,OAChD2F,EAAO7F,KAAK,CAACrB,EAAK2G,GACpB,CACF,CACAQ,EAAS,EACX,CAGF,CAGA,IAAMM,EAAcN,EAAO5F,OAC3B,GAAIkG,EAAa,CACf,IAAMC,EAAaD,EAAYlI,QAAQ,KACvC,GAAImI,EAAa,EAAG,CAClB,IAAM1H,EAAMyH,EAAYjH,MAAM,EAAGkH,GAAYnG,OACvCoF,EAAQc,EAAYjH,MAAMkH,EAAa,GAAGnG,OAChD2F,EAAO7F,KAAK,CAACrB,EAAK2G,GACpB,CACF,CAEA,OAAOO,CACT,CASWS,CAAoBhB,GAAO1O,OAAO,SAAUiP,EAAMU,OAAG5H,EAAG4H,KAAEjB,EAAKiB,KAUpE,OAFAV,EALsBlH,EAAIpB,QAAQ,YAAa,SAAAiJ,UAC7CA,EAAO,GAAGpB,aAAa,IAIDO,EAAcL,EAAO7B,EAAK9E,GAE3CkH,CACT,EAAG,KAC+C,IAAzCnO,EAAuBwG,QAAQS,GACjCgH,EAAcL,EAAO7B,EAAK9E,IACxB2G,EAAM/I,MAAMlD,KAErBiM,EAAQA,EAAMnG,MAAM,EAAGmG,EAAMzH,OAAS,IAG1B,SAAVyH,GAEiB,UAAVA,GAIJA,EACT,CAwgBkDmB,CACxChD,EACA9E,EACA2G,EACAxB,EAAQhC,WAImB,iBAApB4D,IACN1M,EAAqBuG,KAAKmG,IACzBtM,EAA4BmG,KAAKmG,MAEnCjI,EAAIgI,GAAapB,EAAQqB,EAAgBxF,QAE7C,KAAmB,UAAR8E,IACTvH,EAAI9G,EAA0BqO,IAAQA,IAAO,GAG/C,OAAOvH,CACT,EAAG,QACL,UA5IAoG,IAAAA,EAAmB,aACnBC,IAAAA,EAAiC,IAEjCA,EAAQJ,UAAYI,EAAQJ,WAAa,GACzCI,EAAQhC,UAAYgC,EAAQhC,WAAaA,GACzCgC,EAAQ1E,QAAU0E,EAAQ1E,SAAWA,GACrC0E,EAAQ5M,oBAAsB4M,EAAQ5M,oBAAmBiN,KAChDjN,EAAwB4M,EAAQ5M,qBACrCA,EAEJ4M,EAAQI,cAAgBJ,EAAQI,eAAiBwC,EAAMxC,cAwJvD,IAAMyC,EAAwD,GACxDC,EAA6D,GAQ7DC,KAAKC,MACRtS,EAASC,YAAa,CACrB8H,MAAO+E,GAAWxJ,GAClBmF,QACAC,eAAMC,EAASD,EAAOR,GACpB,IAAAqK,EAA2B5J,EAAQ,GAChCI,QAAQxF,EAAkC,IAC1CwE,MAAMvE,GAET,MAAO,CACLgP,MALYD,KAMZjG,SAAU5D,EANW6J,KAMIrK,GAE7B,EACA8B,gBAAOC,EAAMC,EAAQhC,GACnB,IAAMqH,EAAQ,CACZpF,IAAKjC,EAAMiC,KAiBb,OAdIF,EAAKuI,QACPjD,EAAMK,UACJ,kBACAN,EAAQ1E,QAAQX,EAAKuI,MAAMjQ,cAAeqI,IAE5CX,EAAKqC,SAASmG,QAAQ,CACpBC,MAAO,GACPpG,SAAU,CAAC,CAAE1F,KAAM5G,EAAS4B,KAAMA,KAAMqI,EAAKuI,QAC7CG,cAAc,EACd/L,KAAM5G,EAASY,UACfqO,IAAK,YAIFvH,EAAE,aAAc6H,EAAOrF,EAAOD,EAAKqC,SAAUpE,GACtD,GACDoK,EAEAtS,EAASE,WAAY,CACpB6H,MAAOgF,GAActJ,GACrBgF,QACAC,MAAOyF,GACPnE,gBAAO0G,EAAGkC,EAAI1K,GACZ,OAAOR,QAAIyC,IAAKjC,EAAMiC,KACxB,GACDmI,EAEAtS,EAASG,eAAgB,CACxB4H,MAAO+E,GAAWpJ,GAClB+E,QACAC,MAAOyF,GACPnE,gBAAO0G,EAAGkC,EAAI1K,GACZ,OAAOR,QAAIyC,IAAKjC,EAAMiC,KACxB,GACDmI,EAEAtS,EAASI,WAAY,CACpB2H,MAAO+E,GAAWlJ,GAClB6E,QACAC,eAAMC,GACJ,MAAO,CACLkK,UAAMhK,EACNjH,KAAMkI,GAAQnB,EAAQ,GAAGI,QAAQ,UAAW,KAAKA,QAC/C5C,GACA,MAGN,EAEA6D,gBAAOC,EAAMC,EAAQhC,GACnB,OACER,SAAKyC,IAAKjC,EAAMiC,KACdzC,cACMuC,EAAKyI,OACT9C,UAAW3F,EAAK4I,aAAe5I,EAAK4I,KAAS,KAE5C5I,EAAKrI,MAId,GAKA0Q,EAEDtS,EAASK,YAAa,CACrB0H,MAAO+E,GAAWnJ,GAClB8E,QACAC,eAAMC,GACJ,MAAO,CAEL+J,MAAOpC,EAAgB,OAAQ3H,EAAQ,IAAM,IAC7CkK,KAAMlK,EAAQ,SAAME,EACpBjH,KAAM+G,EAAQ,GACd/B,KAAM5G,EAASI,UAEnB,GACDkS,EAEAtS,EAASM,YAAa,CACrByH,MAAO8E,GAAkBhJ,GACzB4E,QACAC,eAAMC,GACJ,MAAO,CACL/G,KAAM+G,EAAQ,GAAGI,QAAQ5C,GAAiB,MAE9C,EACA6D,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOR,UAAMyC,IAAKjC,EAAMiC,KAAMF,EAAKrI,KACrC,GACD0Q,EAKAtS,EAASO,UAAW,CACnBwH,MAAO+E,GAAW9I,GAClByE,QACAC,eAAMC,GAMJ,OALAwJ,EAAU3G,KAAK,CACbjL,SAAUoI,EAAQ,GAClBmK,WAAYnK,EAAQ,KAGf,EACT,EACAqB,OAAQoE,IACTkE,EAEAtS,EAASQ,mBAAoB,CAC5BuH,MAAO4E,GAAY1I,GACnBwE,QACAC,eAAMC,GACJ,MAAO,CACLoK,WAAYzD,EAAQ1E,QAAQjC,EAAQ,GAAIiC,IACxChJ,KAAM+G,EAAQ,GAElB,EACAqB,gBAAOC,EAAMC,EAAQhC,GACnB,OACER,OAAGyC,IAAKjC,EAAMiC,IAAK6I,KAAM1D,EAAQhC,UAAUrD,EAAK8I,OAAQ,IAAK,SAC3DrL,SAAKyC,IAAKjC,EAAMiC,KAAMF,EAAKrI,MAGjC,GACuD0Q,EAExDtS,EAASS,SAAU,CAClBsH,MAAO4E,GAAYvI,GACnBqE,QACAC,eAAMC,GACJ,MAAO,CACLsK,UAAwC,MAA7BtK,EAAQ,GAAGpG,cAE1B,EACAyH,gBAAOC,EAAMC,EAAQhC,GACnB,OACER,WACEwL,QAASjJ,EAAKgJ,UACd9I,IAAKjC,EAAMiC,IACXgJ,YACAvM,KAAK,YAGX,GAC6C0L,EAE9CtS,EAASU,SAAU,CAClBqH,MAAO+E,GACLwC,EAAQ8D,mBAAqB9O,EAA0BD,GAEzDoE,QACAC,eAAMC,EAASD,EAAOR,GACpB,MAAO,CACLoE,SAAUuB,GAAYnF,EAAOC,EAAQ,GAAIT,GACzCmL,GAAI/D,EAAQ1E,QAAQjC,EAAQ,GAAIiC,IAChC0I,MAAO3K,EAAQ,GAAGU,OAEtB,EACAW,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOR,MACDuC,EAAKqJ,MACT,CAAED,GAAIpJ,EAAKoJ,GAAIlJ,IAAKjC,EAAMiC,KAC1BD,EAAOD,EAAKqC,SAAUpE,GAE1B,GACDoK,EAEAtS,EAASW,eAAgB,CACxBoH,MAAO+E,GAAWvI,GAClBkE,QACAC,eAAMC,EAASD,EAAOR,GACpB,MAAO,CACLoE,SAAUuB,GAAYnF,EAAOC,EAAQ,GAAIT,GACzCoL,MAAsB,MAAf3K,EAAQ,GAAa,EAAI,EAChC/B,KAAM5G,EAASU,QAEnB,GACD4R,EAEAtS,EAASY,WAAY,CAIpBmH,MAAOgF,GAAcvI,GACrBiE,QACAC,eAAMC,EAASD,EAAOR,GACpB,IA/gCqBqF,EA+gCrBgG,EAAuB5K,EAAQ,GAAGZ,MAAMzB,IAElCkN,EAAU,IAAI1N,WAFDyN,KAE0B,MACvCE,EAAU9K,EAAQ,GAAGI,QAAQyK,EAAS,IAEtCE,GAphCenG,EAohCiBkG,EAnhCrClJ,GAAe4C,KAAK,SAAAwG,UAAKA,EAAE5I,KAAKwC,EAAM,GAohCnCU,GACAJ,IAEE+F,EAAUjL,EAAQ,GAAGpG,cACrBoQ,GAC+C,IAAnD1P,EAA6ByG,QAAQkK,GAEjC3E,GACJ0D,EAAeiB,EAAUjL,EAAQ,IACjC+C,OAEImI,EAAM,CACVnB,MAAOpC,EAAgBrB,EAAKtG,EAAQ,IACpCgK,aAAcA,EACd1D,IAAAA,GAuBF,OAdA/G,EAAM4L,SAAW5L,EAAM4L,UAAwB,MAAZF,EAE/BjB,EACFkB,EAAIjS,KAAO+G,EAAQ,GAEnBkL,EAAIvH,SAAWoH,EAAUhL,EAAO+K,EAASvL,GAO3CA,EAAM4L,UAAW,EAEVD,CACT,EACA7J,gBAAOC,EAAMC,EAAQhC,GACnB,OACER,EAACuC,EAAKgF,IAAGU,GAACxF,IAAKjC,EAAMiC,KAASF,EAAKyI,OAChCzI,EAAKrI,OAASqI,EAAKqC,SAAWpC,EAAOD,EAAKqC,SAAUpE,GAAS,IAGpE,GACDoK,EAEAtS,EAASc,iBAAkB,CAI1BiH,MAAOgF,GAAcnI,GACrB6D,QACAC,eAAMC,GACJ,IAAMsG,EAAMtG,EAAQ,GAAG+C,OACvB,MAAO,CACLgH,MAAOpC,EAAgBrB,EAAKtG,EAAQ,IAAM,IAC1CsG,IAAAA,EAEJ,EACAjF,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOR,EAACuC,EAAKgF,IAAGU,KAAK1F,EAAKyI,OAAOvI,IAAKjC,EAAMiC,MAC9C,GACDmI,EAEAtS,EAASa,aAAc,CACtBkH,MAAOgF,GAAcrI,GACrB+D,QACAC,iBACE,MAAO,EACT,EACAsB,OAAQoE,IACTkE,EAEAtS,EAASe,OAAQ,CAChBgH,MAAO8E,GAAkBxC,IACzB5B,QACAC,eAAMC,GACJ,MAAO,CACLoL,IAAKpL,EAAQ,GACboK,OAAQpF,GAAYhF,EAAQ,IAC5BqL,MAAOrL,EAAQ,GAEnB,EACAqB,gBAAOC,EAAMC,EAAQhC,GACnB,OACER,SACEyC,IAAKjC,EAAMiC,IACX4J,IAAK9J,EAAK8J,UAAOlL,EACjBmL,MAAO/J,EAAK+J,YAASnL,EACrB6F,IAAKY,EAAQhC,UAAUrD,EAAK8I,OAAQ,MAAO,QAGjD,GAKAT,EAEDtS,EAASgB,MAAO,CACf+G,MAAO4E,GAAYvC,IACnB3B,QACAC,eAAMC,EAASD,EAAOR,GACpB,MAAO,CACLoE,SAAU0B,GAAkBtF,EAAOC,EAAQ,GAAIT,GAC/C6K,OAAQpF,GAAYhF,EAAQ,IAC5BqL,MAAOrL,EAAQ,GAEnB,EACAqB,gBAAOC,EAAMC,EAAQhC,GACnB,OACER,OACEyC,IAAKjC,EAAMiC,IACX6I,KAAM1D,EAAQhC,UAAUrD,EAAK8I,OAAQ,IAAK,QAC1CiB,MAAO/J,EAAK+J,OAEX9J,EAAOD,EAAKqC,SAAUpE,GAG7B,GACDoK,EAGAtS,EAASiB,6BAA8B,CACtC8G,MAAO4E,GAAY3H,GACnByD,QACAC,eAAMC,GACJ,MAAO,CACL2D,SAAU,CACR,CACE1K,KAAM+G,EAAQ,GACd/B,KAAM5G,EAAS4B,OAGnBmR,OAAQpK,EAAQ,GAChB/B,KAAM5G,EAASgB,KAEnB,GACDsR,EAEAtS,EAASkB,qBAAsB,CAC9B6G,MAAOC,GAAY,SAACC,EAAQC,GAC1B,OAAIA,EAAM4L,UAAYxE,EAAQ2E,qBAIvBtH,GAAY7H,EAAZ6H,CAAsC1E,EAAQC,EACvD,GACAO,QACAC,eAAMC,GACJ,MAAO,CACL2D,SAAU,CACR,CACE1K,KAAM+G,EAAQ,GACd/B,KAAM5G,EAAS4B,OAGnBmR,OAAQpK,EAAQ,GAChBqL,WAAOnL,EACPjC,KAAM5G,EAASgB,KAEnB,GACDsR,EAEAtS,EAASmB,oBAAqB,CAC7B4G,MAAO4E,GAAY5H,GACnB0D,QACAC,eAAMC,GACJ,IAAIuL,EAAUvL,EAAQ,GAClBoK,EAASpK,EAAQ,GAOrB,OAJKvF,EAAwB2H,KAAKgI,KAChCA,EAAS,UAAYA,GAGhB,CACLzG,SAAU,CACR,CACE1K,KAAMsS,EAAQnL,QAAQ,UAAW,IACjCnC,KAAM5G,EAAS4B,OAGnBmR,OAAQA,EACRnM,KAAM5G,EAASgB,KAEnB,GACDsR,EAEAtS,EAASqB,aAAcoG,GACtBC,EA38CqB,GA68C+B4K,EAErDtS,EAASkC,eAAgBuF,GACxBC,EA/8CuB,GAi9C+B4K,EAEvDtS,EAASoB,kBAAmB,CAC3B2G,MAAO+E,GAAWhJ,GAClB2E,QACAC,MAAOyF,GACPnE,kBACE,MAAO,IACT,GACDsI,EAEAtS,EAASsB,WAAY,CACpByG,MAAOC,GAAYgF,IACnBvE,QACAC,MAAOwF,GACPlE,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOR,OAAGyC,IAAKjC,EAAMiC,KAAMD,EAAOD,EAAKqC,SAAUpE,GACnD,GAC4DoK,EAE7DtS,EAASuB,KAAM,CACdwG,MAAO4E,GAAYxH,GACnBsD,QACAC,eAAMC,GAMJ,OALAyJ,EAAKzJ,EAAQ,IAAM,CACjBoK,OAAQpK,EAAQ,GAChBqL,MAAOrL,EAAQ,IAGV,EACT,EACAqB,OAAQoE,IACTkE,EAEAtS,EAASwB,UAAW,CACnBuG,MAAO8E,GAAkBzH,GACzBqD,QACAC,eAAMC,GACJ,MAAO,CACLoL,IAAKpL,EAAQ,SAAME,EACnBtH,IAAKoH,EAAQ,GAEjB,EACAqB,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOkK,EAAKnI,EAAK1I,KACfmG,SACEyC,IAAKjC,EAAMiC,IACX4J,IAAK9J,EAAK8J,IACVrF,IAAKY,EAAQhC,UAAU8E,EAAKnI,EAAK1I,KAAKwR,OAAQ,MAAO,OACrDiB,MAAO5B,EAAKnI,EAAK1I,KAAKyS,QAEtB,IACN,GACoD1B,EAErDtS,EAASyB,SAAU,CAClBsG,MAAO4E,GAAYtH,GACnBoD,QACAC,eAAMC,EAASD,EAAOR,GACpB,MAAO,CACLoE,SAAU5D,EAAMC,EAAQ,GAAIT,GAC5BiM,iBAAkBxL,EAAQ,GAC1BpH,IAAKoH,EAAQ,GAEjB,EACAqB,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOkK,EAAKnI,EAAK1I,KACfmG,OACEyC,IAAKjC,EAAMiC,IACX6I,KAAM1D,EAAQhC,UAAU8E,EAAKnI,EAAK1I,KAAKwR,OAAQ,IAAK,QACpDiB,MAAO5B,EAAKnI,EAAK1I,KAAKyS,OAErB9J,EAAOD,EAAKqC,SAAUpE,IAGzBR,UAAMyC,IAAKjC,EAAMiC,KAAMF,EAAKkK,iBAEhC,GACD7B,EAEAtS,EAAS0B,OAAQ,CAChBqG,MAAO+E,GAAW5H,GAClBuD,QACAC,MAAOuD,GACPjC,gBAAOC,EAAMC,EAAQhC,GACnB,IAAMxG,EAAQuI,EACd,OACEvC,WAAOyC,IAAKjC,EAAMiC,KAChBzC,eACEA,YACGhG,EAAM2K,OAAOpD,IAAI,SAA4BM,EAASJ,GACrD,OACEzB,QAAIyC,IAAKhB,EAAGiL,MAAO7H,GAAc7K,EAAOyH,IACrCe,EAAOX,EAASrB,GAGvB,KAIJR,eACGhG,EAAM0J,MAAMnC,IAAI,SAA0BoL,EAAKlL,GAC9C,OACEzB,QAAIyC,IAAKhB,GACNkL,EAAIpL,IAAI,SAA2BM,EAAS+K,GAC3C,OACE5M,QAAIyC,IAAKmK,EAAGF,MAAO7H,GAAc7K,EAAO4S,IACrCpK,EAAOX,EAASrB,GAGvB,GAGN,IAIR,GACDoK,EAEAtS,EAAS4B,MAAO,CAKfmG,MAAOgF,GAAc3G,IACrBqC,QACAC,eAAMC,GACJ,MAAO,CACL/G,KAAM+G,EAAQ,GAEXI,QAAQtE,EAAkB,SAAC8P,EAAMC,GAChC,OAAOlF,EAAQ5M,oBAAoB8R,GAC/BlF,EAAQ5M,oBAAoB8R,GAC5BD,CACN,GAEN,EACAvK,gBAAOC,GACL,OAAOA,EAAKrI,IACd,GACD0Q,EAEAtS,EAAS6B,YAAa,CACrBkG,MAAO8E,GAAkBhH,GACzB4C,QACAC,eAAMC,EAASD,EAAOR,GACpB,MAAO,CAGLoE,SAAU5D,EAAMC,EAAQ,GAAIT,GAEhC,EACA8B,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOR,YAAQyC,IAAKjC,EAAMiC,KAAMD,EAAOD,EAAKqC,SAAUpE,GACxD,GACDoK,EAEAtS,EAAS8B,gBAAiB,CACzBiG,MAAO8E,GAAkB9G,GACzB0C,QACAC,eAAMC,EAASD,EAAOR,GACpB,MAAO,CAGLoE,SAAU5D,EAAMC,EAAQ,GAAIT,GAEhC,EACA8B,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOR,QAAIyC,IAAKjC,EAAMiC,KAAMD,EAAOD,EAAKqC,SAAUpE,GACpD,GACDoK,EAEAtS,EAAS+B,aAAc,CAKtBgG,MAAO8E,GAAkB3G,IACzBuC,QACAC,eAAMC,GACJ,MAAO,CACL/G,KAAM+G,EAAQ,GACd/B,KAAM5G,EAAS4B,KAEnB,GACD0Q,EAEAtS,EAASgC,YAAa,CACrB+F,MAAO8E,GAAkB7G,GACzByC,QACAC,MAAOwF,GACPlE,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOR,UAAMyC,IAAKjC,EAAMiC,KAAMD,EAAOD,EAAKqC,SAAUpE,GACtD,GACDoK,EAEAtS,EAASiC,qBAAsB,CAC9B8F,MAAO8E,GAAkB5G,IACzBwC,QACAC,MAAOwF,GACPlE,gBAAOC,EAAMC,EAAQhC,GACnB,OAAOR,SAAKyC,IAAKjC,EAAMiC,KAAMD,EAAOD,EAAKqC,SAAUpE,GACrD,GACDoK,IAkCmC,IAAlChD,EAAQmF,+BACHpC,GAAMrS,EAASY,kBACfyR,GAAMrS,EAASc,kBAGxB,IA/4BgBkJ,GA+4BViG,GA/qCR,SACEoC,GAOA,IAAIqC,EAAWC,OAAOC,KAAKvC,GA8B3B,SAASwC,EACP5M,EACAC,GAEA,IACI4M,EAEAC,EAHAhL,EAAS,GAETiL,EAAW,GAEXC,EAAoB,GAQxB,IANA/M,EAAMG,YAAcH,EAAMG,aAAe,GAMlCJ,GAEL,IADA,IAAIkB,EAAI,EACDA,EAAIuL,EAASrL,QAIlB,GAFAyL,EAAOzC,EADP2C,EAAWN,EAASvL,KAGhBjB,EAAMK,QAAWuM,EAAK/M,MAAMQ,OAAhC,CAKA,IAAMI,EAAUmM,EAAK/M,MAAME,EAAQC,GAEnC,GAAIS,EAAS,CAIXT,EAAMG,aAHN4M,EAAoBtM,EAAQ,GAK5BV,EAASA,EAAOiN,UAAUD,EAAkB5L,QAQzB,OANnB0L,EAASD,EAAKpM,MAAMC,EAASkM,EAAa3M,IAM/BtB,OACTmO,EAAOnO,KAAOoO,GAGhBjL,EAAOyB,KAAKuJ,GACZ,KACF,CAEA5L,GA1BA,MAFEA,IAmCN,OAFAjB,EAAMG,YAAc,GAEb0B,CACT,CAEA,OA3EA2K,EAASS,KAAK,SAAUC,EAAOC,GAC7B,IAAIC,EAASjD,EAAM+C,GAAO3M,MACtB8M,EAASlD,EAAMgD,GAAO5M,MAG1B,OAAI6M,IAAWC,EACND,EAASC,EACPH,EAAQC,GACT,GAIZ,YA+D2BpN,EAAQC,GACjC,OAAO2M,EA9HX,SAA6B5M,GAC3B,OAAOA,EACJc,QAAQhF,EAAc,MACtBgF,QAAQ7E,EAAY,IACpB6E,QAAQxD,EAAO,OACpB,CAyHuBiQ,CAAoBvN,GAASC,EAClD,CACF,CAykCiBuN,CAAUpD,IACnBrC,IAh5BUhG,GAqClB,SACEqI,EACAqD,GAEA,gBACE7B,EACA7J,EACA9B,GAEA,IAAMyN,EAAWtD,EAAMwB,EAAIjN,MAAMoD,OAEjC,OAAO0L,EACHA,EAAW,kBAAMC,EAAS9B,EAAK7J,EAAQ9B,EAAM,EAAE2L,EAAK7J,EAAQ9B,GAC5DyN,EAAS9B,EAAK7J,EAAQ9B,EAC5B,CACF,CA41BqC0N,CAAevD,GAAO/C,EAAQuG,qBA/4BjDC,EACdjC,EACA3L,GAEA,YAFAA,IAAAA,EAA6B,IAEzB6N,MAAMC,QAAQnC,GAAM,CAQtB,IAPA,IAAMoC,EAAS/N,EAAMiC,IACfJ,EAAS,GAIXmM,GAAgB,EAEX/M,EAAI,EAAGA,EAAI0K,EAAIxK,OAAQF,IAAK,CACnCjB,EAAMiC,IAAMhB,EAEZ,IAAMgN,EAAUL,EAAcjC,EAAI1K,GAAIjB,GAChCkO,EAA8B,iBAAZD,EAEpBC,GAAYF,EACdnM,EAAOA,EAAOV,OAAS,IAAM8M,EACR,OAAZA,GACTpM,EAAOyB,KAAK2K,GAGdD,EAAgBE,CAClB,CAIA,OAFAlO,EAAMiC,IAAM8L,EAELlM,CACT,CAEA,OAAOC,GAAO6J,EAAKiC,EAAe5N,EACpC,GAg3BMkI,GAAMP,EAAQR,GAEpB,OAAI8C,EAAU9I,OAEV3B,aACG0I,GACD1I,YAAQyC,IAAI,UACTgI,EAAUlJ,IAAI,SAAwBoN,GACrC,OACE3O,SACE2L,GAAI/D,EAAQ1E,QAAQyL,EAAIvD,WAAYlI,IACpCT,IAAKkM,EAAIvD,YAERuD,EAAIvD,WACJ9C,GAAQC,GAAOoG,EAAI9V,SAAU,CAAEgI,QAAQ,KAG9C,KAMD6H,EACT,gBAWI,SALUkG,WAKPhK,SAAAA,WAAQiK,EAAG,GAAEA,EAAEjH,EAAOgH,EAAPhH,QAAYC,oIAAKiH,CAAAF,EAAAG,GAQrC,OAAOvE,EAAMwE,aACXtH,GAAS9C,EAAUgD,GACnBC,EAEJ"}