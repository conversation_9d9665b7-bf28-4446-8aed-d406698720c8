import*as e from"react";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e},r.apply(this,arguments)}var n,t=["children","options"],i={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"};!function(e){e[e.MAX=0]="MAX",e[e.HIGH=1]="HIGH",e[e.MED=2]="MED",e[e.LOW=3]="LOW",e[e.MIN=4]="MIN"}(n||(n={}));var a=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce(function(e,r){return e[r.toLowerCase()]=r,e},{class:"className",for:"htmlFor"}),o={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},l=["style","script"],c=["src","href","data","formAction","srcDoc","action"],u=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,s=/mailto:/i,f=/\n{2,}$/,d=/^(\s*>[\s\S]*?)(?=\n\n|$)/,p=/^ *> ?/gm,h=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,m=/^ {2,}\n/,g=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,y=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,v=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,k=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,x=/^(?:\n *)*\n/,b=/\r\n?/g,C=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,S=/^\[\^([^\]]+)]/,w=/\f/g,E=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,z=/^\s*?\[(x|\s)\]/,L=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,A=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,O=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,T=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,B=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,$=/^<!--[\s\S]*?(?:-->)/,M=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,R=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,I=/^\{.*\}$/,D=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,U=/^<([^ >]+@[^ >]+)>/,N=/^<([^ >]+:\/[^ >]+)>/,j=/-([a-z])?/gi,H=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,P=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,_=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,F=/^\[([^\]]*)\] ?\[([^\]]*)\]/,W=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,G=/\t/g,Z=/(^ *\||\| *$)/g,q=/^ *:-+: *$/,Q=/^ *:-+ *$/,V=/^ *-+: *$/,X="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",J=new RegExp("^([*_])\\1"+X+"\\1\\1(?!\\1)"),K=new RegExp("^([*_])"+X+"\\1(?!\\1)"),Y=new RegExp("^(==)"+X+"\\1"),ee=new RegExp("^(~~)"+X+"\\1"),re=/^\\([^0-9A-Za-z\s])/,ne=/\\([^0-9A-Za-z\s])/g,te=/^([\s\S](?:(?!  |[0-9]\.)[^=*_~\-\n<`\\\[!])*)/,ie=/^\n+/,ae=/^([ \t]*)/,oe=/\\([^\\])/g,le=/(?:^|\n)( *)$/,ce="(?:\\d+\\.)",ue="(?:[*+-])";function se(e){return"( *)("+(1===e?ce:ue)+") +"}var fe=se(1),de=se(2);function pe(e){return new RegExp("^"+(1===e?fe:de))}var he=pe(1),me=pe(2);function ge(e){return new RegExp("^"+(1===e?fe:de)+"[^\\n]*(?:\\n(?!\\1"+(1===e?ce:ue)+" )[^\\n]*)*(\\n|$)","gm")}var ye=ge(1),ve=ge(2);function ke(e){var r=1===e?ce:ue;return new RegExp("^( *)("+r+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+r+" (?!"+r+" ))\\n*|\\s*\\n*$)")}var xe=ke(1),be=ke(2);function Ce(e,r){var n=1===r,t=n?xe:be,a=n?ye:ve,o=n?he:me;return{match:Me(function(e,r){var n=le.exec(r.prevCapture);return n&&(r.list||!r.inline&&!r.simple)?t.exec(e=n[1]+e):null}),order:1,parse:function(e,r,t){var i=n?+e[2]:void 0,l=e[0].replace(f,"\n").match(a),c=!1;return{items:l.map(function(e,n){var i=o.exec(e)[0].length,a=new RegExp("^ {1,"+i+"}","gm"),u=e.replace(a,"").replace(o,""),s=n===l.length-1,f=-1!==u.indexOf("\n\n")||s&&c;c=f;var d,p=t.inline,h=t.list;t.list=!0,f?(t.inline=!1,d=Le(u)+"\n\n"):(t.inline=!0,d=Le(u));var m=r(d,t);return t.inline=p,t.list=h,m}),ordered:n,start:i}},render:function(r,n,t){return e(r.ordered?"ol":"ul",{key:t.key,start:r.type===i.orderedList?r.start:void 0},r.items.map(function(r,i){return e("li",{key:i},n(r,t))}))}}}var Se=new RegExp("^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['\"]([\\s\\S]*?)['\"])?\\s*\\)"),we=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,Ee=[d,y,v,L,O,A,H,xe,be],ze=[].concat(Ee,[/^[^\n]+(?:  \n|\n{2,})/,T,$,R]);function Le(e){for(var r=e.length;r>0&&e[r-1]<=" ";)r--;return e.slice(0,r)}function Ae(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Oe(e){return V.test(e)?"right":q.test(e)?"center":Q.test(e)?"left":null}function Te(e,r,n,t){var i=n.inTable;n.inTable=!0;var a=[[]],o="";function l(){if(o){var e=a[a.length-1];e.push.apply(e,r(o,n)),o=""}}return e.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach(function(e,r,n){"|"===e.trim()&&(l(),t)?0!==r&&r!==n.length-1&&a.push([]):o+=e}),l(),n.inTable=i,a}function Be(e,r,n){n.inline=!0;var t=e[2]?e[2].replace(Z,"").split("|").map(Oe):[],a=e[3]?function(e,r,n){return e.trim().split("\n").map(function(e){return Te(e,r,n,!0)})}(e[3],r,n):[],o=Te(e[1],r,n,!!a.length);return n.inline=!1,a.length?{align:t,cells:a,header:o,type:i.table}:{children:o,type:i.paragraph}}function $e(e,r){return null==e.align[r]?{}:{textAlign:e.align[r]}}function Me(e){return e.inline=1,e}function Re(e){return Me(function(r,n){return n.inline?e.exec(r):null})}function Ie(e){return Me(function(r,n){return n.inline||n.simple?e.exec(r):null})}function De(e){return function(r,n){return n.inline||n.simple?null:e.exec(r)}}function Ue(e){return Me(function(r){return e.exec(r)})}function Ne(e,r){if(r.inline||r.simple)return null;var n="";e.split("\n").every(function(e){return e+="\n",!Ee.some(function(r){return r.test(e)})&&(n+=e,!!e.trim())});var t=Le(n);return""==t?null:[n,,t]}var je=/(javascript|vbscript|data(?!:image)):/i;function He(e){try{var r=decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"");if(je.test(r))return null}catch(e){return null}return e}function Pe(e){return e.replace(oe,"$1")}function _e(e,r,n){var t=n.inline||!1,i=n.simple||!1;n.inline=!0,n.simple=!0;var a=e(r,n);return n.inline=t,n.simple=i,a}function Fe(e,r,n){var t=n.inline||!1,i=n.simple||!1;n.inline=!1,n.simple=!0;var a=e(r,n);return n.inline=t,n.simple=i,a}function We(e,r,n){var t=n.inline||!1;n.inline=!1;var i=e(r,n);return n.inline=t,i}var Ge=function(e,r,n){return{children:_e(r,e[2],n)}};function Ze(){return{}}function qe(){return null}function Qe(){return[].slice.call(arguments).filter(Boolean).join(" ")}function Ve(e,r,n){for(var t=e,i=r.split(".");i.length&&void 0!==(t=t[i[0]]);)i.shift();return t||n}function Xe(e,r){var n=Ve(r,e);return n?"function"==typeof n||"object"==typeof n&&"render"in n?n:Ve(r,e+".component",e):e}function Je(n,t){var f;function Z(e,n){var i,a=Ve(t.overrides,e+".props",{});return(i=t).createElement.apply(i,[Xe(e,t.overrides),r({},n,a,{className:Qe(null==n?void 0:n.className,a.className)||void 0})].concat([].slice.call(arguments,2)))}function q(e){e=e.replace(E,"");var r=!1;t.forceInline?r=!0:t.forceBlock||(r=!1===W.test(e));for(var n=ue(ce(r?e:Le(e).replace(ie,"")+"\n\n",{inline:r}));"string"==typeof n[n.length-1]&&!n[n.length-1].trim();)n.pop();if(null===t.wrapper)return n;var i,a=t.wrapper||(r?"span":"div");if(n.length>1||t.forceWrapper)i=n;else{if(1===n.length)return"string"==typeof(i=n[0])?Z("span",{key:"outer"},i):i;i=null}return t.createElement(a,{key:"outer"},i)}function Q(e,r){var n=r.match(u);return n?n.reduce(function(r,n){var i=n.indexOf("=");if(-1!==i){var o=function(e){return-1!==e.indexOf("-")&&null===e.match(M)&&(e=e.replace(j,function(e,r){return r.toUpperCase()})),e}(n.slice(0,i)).trim(),l=function(e){var r=e[0];return('"'===r||"'"===r)&&e.length>=2&&e[e.length-1]===r?e.slice(1,-1):e}(n.slice(i+1).trim()),u=a[o]||o;if("ref"===u)return r;var s=r[u]=function(e,r,n,t){return"style"===r?function(e){var r=[],n="",t=!1,i=!1,a="";if(!e)return r;for(var o=0;o<e.length;o++){var l=e[o];if('"'!==l&&"'"!==l||t||(i?l===a&&(i=!1,a=""):(i=!0,a=l)),"("===l&&n.endsWith("url")?t=!0:")"===l&&t&&(t=!1),";"!==l||i||t)n+=l;else{var c=n.trim();if(c){var u=c.indexOf(":");if(u>0){var s=c.slice(0,u).trim(),f=c.slice(u+1).trim();r.push([s,f])}}n=""}}var d=n.trim();if(d){var p=d.indexOf(":");if(p>0){var h=d.slice(0,p).trim(),m=d.slice(p+1).trim();r.push([h,m])}}return r}(n).reduce(function(r,n){var i=n[0],a=n[1];return r[i.replace(/(-[a-z])/g,function(e){return e[1].toUpperCase()})]=t(a,e,i),r},{}):-1!==c.indexOf(r)?t(n,e,r):(n.match(I)&&(n=n.slice(1,n.length-1)),"true"===n||"false"!==n&&n)}(e,o,l,t.sanitizer);"string"==typeof s&&(T.test(s)||R.test(s))&&(r[u]=q(s.trim()))}else"style"!==n&&(r[a[n]||n]=!0);return r},{}):null}void 0===n&&(n=""),void 0===t&&(t={}),t.overrides=t.overrides||{},t.sanitizer=t.sanitizer||He,t.slugify=t.slugify||Ae,t.namedCodesToUnicode=t.namedCodesToUnicode?r({},o,t.namedCodesToUnicode):o,t.createElement=t.createElement||e.createElement;var V=[],X={},oe=((f={})[i.blockQuote]={match:De(d),order:1,parse:function(e,r,n){var t=e[0].replace(p,"").match(h);return{alert:t[1],children:r(t[2],n)}},render:function(e,r,n){var a={key:n.key};return e.alert&&(a.className="markdown-alert-"+t.slugify(e.alert.toLowerCase(),Ae),e.children.unshift({attrs:{},children:[{type:i.text,text:e.alert}],noInnerParse:!0,type:i.htmlBlock,tag:"header"})),Z("blockquote",a,r(e.children,n))}},f[i.breakLine]={match:Ue(m),order:1,parse:Ze,render:function(e,r,n){return Z("br",{key:n.key})}},f[i.breakThematic]={match:De(g),order:1,parse:Ze,render:function(e,r,n){return Z("hr",{key:n.key})}},f[i.codeBlock]={match:De(v),order:0,parse:function(e){return{lang:void 0,text:Le(e[0].replace(/^ {4}/gm,"")).replace(ne,"$1")}},render:function(e,n,t){return Z("pre",{key:t.key},Z("code",r({},e.attrs,{className:e.lang?"lang-"+e.lang:""}),e.text))}},f[i.codeFenced]={match:De(y),order:0,parse:function(e){return{attrs:Q("code",e[3]||""),lang:e[2]||void 0,text:e[4],type:i.codeBlock}}},f[i.codeInline]={match:Ie(k),order:3,parse:function(e){return{text:e[2].replace(ne,"$1")}},render:function(e,r,n){return Z("code",{key:n.key},e.text)}},f[i.footnote]={match:De(C),order:0,parse:function(e){return V.push({footnote:e[2],identifier:e[1]}),{}},render:qe},f[i.footnoteReference]={match:Re(S),order:1,parse:function(e){return{target:"#"+t.slugify(e[1],Ae),text:e[1]}},render:function(e,r,n){return Z("a",{key:n.key,href:t.sanitizer(e.target,"a","href")},Z("sup",{key:n.key},e.text))}},f[i.gfmTask]={match:Re(z),order:1,parse:function(e){return{completed:"x"===e[1].toLowerCase()}},render:function(e,r,n){return Z("input",{checked:e.completed,key:n.key,readOnly:!0,type:"checkbox"})}},f[i.heading]={match:De(t.enforceAtxHeadings?A:L),order:1,parse:function(e,r,n){return{children:_e(r,e[2],n),id:t.slugify(e[2],Ae),level:e[1].length}},render:function(e,r,n){return Z("h"+e.level,{id:e.id,key:n.key},r(e.children,n))}},f[i.headingSetext]={match:De(O),order:0,parse:function(e,r,n){return{children:_e(r,e[1],n),level:"="===e[2]?1:2,type:i.heading}}},f[i.htmlBlock]={match:Ue(T),order:1,parse:function(e,r,n){var t,i=e[3].match(ae),a=new RegExp("^"+i[1],"gm"),o=e[3].replace(a,""),c=(t=o,ze.some(function(e){return e.test(t)})?We:_e),u=e[1].toLowerCase(),s=-1!==l.indexOf(u),f=(s?u:e[1]).trim(),d={attrs:Q(f,e[2]),noInnerParse:s,tag:f};return n.inAnchor=n.inAnchor||"a"===u,s?d.text=e[3]:d.children=c(r,o,n),n.inAnchor=!1,d},render:function(e,n,t){return Z(e.tag,r({key:t.key},e.attrs),e.text||(e.children?n(e.children,t):""))}},f[i.htmlSelfClosing]={match:Ue(R),order:1,parse:function(e){var r=e[1].trim();return{attrs:Q(r,e[2]||""),tag:r}},render:function(e,n,t){return Z(e.tag,r({},e.attrs,{key:t.key}))}},f[i.htmlComment]={match:Ue($),order:1,parse:function(){return{}},render:qe},f[i.image]={match:Ie(we),order:1,parse:function(e){return{alt:e[1],target:Pe(e[2]),title:e[3]}},render:function(e,r,n){return Z("img",{key:n.key,alt:e.alt||void 0,title:e.title||void 0,src:t.sanitizer(e.target,"img","src")})}},f[i.link]={match:Re(Se),order:3,parse:function(e,r,n){return{children:Fe(r,e[1],n),target:Pe(e[2]),title:e[3]}},render:function(e,r,n){return Z("a",{key:n.key,href:t.sanitizer(e.target,"a","href"),title:e.title},r(e.children,n))}},f[i.linkAngleBraceStyleDetector]={match:Re(N),order:0,parse:function(e){return{children:[{text:e[1],type:i.text}],target:e[1],type:i.link}}},f[i.linkBareUrlDetector]={match:Me(function(e,r){return r.inAnchor||t.disableAutoLink?null:Re(D)(e,r)}),order:0,parse:function(e){return{children:[{text:e[1],type:i.text}],target:e[1],title:void 0,type:i.link}}},f[i.linkMailtoDetector]={match:Re(U),order:0,parse:function(e){var r=e[1],n=e[1];return s.test(n)||(n="mailto:"+n),{children:[{text:r.replace("mailto:",""),type:i.text}],target:n,type:i.link}}},f[i.orderedList]=Ce(Z,1),f[i.unorderedList]=Ce(Z,2),f[i.newlineCoalescer]={match:De(x),order:3,parse:Ze,render:function(){return"\n"}},f[i.paragraph]={match:Me(Ne),order:3,parse:Ge,render:function(e,r,n){return Z("p",{key:n.key},r(e.children,n))}},f[i.ref]={match:Re(P),order:0,parse:function(e){return X[e[1]]={target:e[2],title:e[4]},{}},render:qe},f[i.refImage]={match:Ie(_),order:0,parse:function(e){return{alt:e[1]||void 0,ref:e[2]}},render:function(e,r,n){return X[e.ref]?Z("img",{key:n.key,alt:e.alt,src:t.sanitizer(X[e.ref].target,"img","src"),title:X[e.ref].title}):null}},f[i.refLink]={match:Re(F),order:0,parse:function(e,r,n){return{children:r(e[1],n),fallbackChildren:e[0],ref:e[2]}},render:function(e,r,n){return X[e.ref]?Z("a",{key:n.key,href:t.sanitizer(X[e.ref].target,"a","href"),title:X[e.ref].title},r(e.children,n)):Z("span",{key:n.key},e.fallbackChildren)}},f[i.table]={match:De(H),order:1,parse:Be,render:function(e,r,n){var t=e;return Z("table",{key:n.key},Z("thead",null,Z("tr",null,t.header.map(function(e,i){return Z("th",{key:i,style:$e(t,i)},r(e,n))}))),Z("tbody",null,t.cells.map(function(e,i){return Z("tr",{key:i},e.map(function(e,i){return Z("td",{key:i,style:$e(t,i)},r(e,n))}))})))}},f[i.text]={match:Ue(te),order:4,parse:function(e){return{text:e[0].replace(B,function(e,r){return t.namedCodesToUnicode[r]?t.namedCodesToUnicode[r]:e})}},render:function(e){return e.text}},f[i.textBolded]={match:Ie(J),order:2,parse:function(e,r,n){return{children:r(e[2],n)}},render:function(e,r,n){return Z("strong",{key:n.key},r(e.children,n))}},f[i.textEmphasized]={match:Ie(K),order:3,parse:function(e,r,n){return{children:r(e[2],n)}},render:function(e,r,n){return Z("em",{key:n.key},r(e.children,n))}},f[i.textEscaped]={match:Ie(re),order:1,parse:function(e){return{text:e[1],type:i.text}}},f[i.textMarked]={match:Ie(Y),order:3,parse:Ge,render:function(e,r,n){return Z("mark",{key:n.key},r(e.children,n))}},f[i.textStrikethroughed]={match:Ie(ee),order:3,parse:Ge,render:function(e,r,n){return Z("del",{key:n.key},r(e.children,n))}},f);!0===t.disableParsingRawHTML&&(delete oe[i.htmlBlock],delete oe[i.htmlSelfClosing]);var le,ce=function(e){var r=Object.keys(e);function n(t,i){var a,o,l=[],c="",u="";for(i.prevCapture=i.prevCapture||"";t;)for(var s=0;s<r.length;)if(a=e[c=r[s]],!i.inline||a.match.inline){var f=a.match(t,i);if(f){i.prevCapture+=u=f[0],t=t.substring(u.length),null==(o=a.parse(f,n,i)).type&&(o.type=c),l.push(o);break}s++}else s++;return i.prevCapture="",l}return r.sort(function(r,n){var t=e[r].order,i=e[n].order;return t!==i?t-i:r<n?-1:1}),function(e,r){return n(function(e){return e.replace(b,"\n").replace(w,"").replace(G,"    ")}(e),r)}}(oe),ue=(le=function(e,r){return function(n,t,i){var a=e[n.type].render;return r?r(function(){return a(n,t,i)},n,t,i):a(n,t,i)}}(oe,t.renderRule),function e(r,n){if(void 0===n&&(n={}),Array.isArray(r)){for(var t=n.key,i=[],a=!1,o=0;o<r.length;o++){n.key=o;var l=e(r[o],n),c="string"==typeof l;c&&a?i[i.length-1]+=l:null!==l&&i.push(l),a=c}return n.key=t,i}return le(r,e,n)}),se=q(n);return V.length?Z("div",null,se,Z("footer",{key:"footer"},V.map(function(e){return Z("div",{id:t.slugify(e.identifier,Ae),key:e.identifier},e.identifier,ue(ce(e.footnote,{inline:!0})))}))):se}export default function(r){var n=r.children,i=void 0===n?"":n,a=r.options,o=function(e,r){if(null==e)return{};var n,t,i={},a=Object.keys(e);for(t=0;t<a.length;t++)r.indexOf(n=a[t])>=0||(i[n]=e[n]);return i}(r,t);return e.cloneElement(Je(i,a),o)}export{i as RuleType,Je as compiler,He as sanitizer,Ae as slugify};
//# sourceMappingURL=index.module.js.map
