!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r(require("react")):"function"==typeof define&&define.amd?define(["react"],r):(e||self).MarkdownToJSX=r(e.React)}(this,function(e){function r(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach(function(n){if("default"!==n){var t=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,t.get?t:{enumerable:!0,get:function(){return e[n]}})}}),r.default=e,r}var n=/*#__PURE__*/r(e);function t(){return t=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e},t.apply(this,arguments)}var i,a=["children","options"],o={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"};!function(e){e[e.MAX=0]="MAX",e[e.HIGH=1]="HIGH",e[e.MED=2]="MED",e[e.LOW=3]="LOW",e[e.MIN=4]="MIN"}(i||(i={}));var l=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce(function(e,r){return e[r.toLowerCase()]=r,e},{class:"className",for:"htmlFor"}),c={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},u=["style","script"],s=["src","href","data","formAction","srcDoc","action"],f=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,d=/mailto:/i,p=/\n{2,}$/,h=/^(\s*>[\s\S]*?)(?=\n\n|$)/,m=/^ *> ?/gm,g=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,y=/^ {2,}\n/,v=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,k=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,x=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,b=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,C=/^(?:\n *)*\n/,S=/\r\n?/g,w=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,E=/^\[\^([^\]]+)]/,O=/\f/g,z=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,L=/^\s*?\[(x|\s)\]/,T=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,A=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,B=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,M=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,R=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,$=/^<!--[\s\S]*?(?:-->)/,j=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,I=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,D=/^\{.*\}$/,U=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,N=/^<([^ >]+@[^ >]+)>/,P=/^<([^ >]+:\/[^ >]+)>/,_=/-([a-z])?/gi,H=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,F=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,W=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,G=/^\[([^\]]*)\] ?\[([^\]]*)\]/,Z=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,q=/\t/g,X=/(^ *\||\| *$)/g,Q=/^ *:-+: *$/,V=/^ *:-+ *$/,J=/^ *-+: *$/,K="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",Y=new RegExp("^([*_])\\1"+K+"\\1\\1(?!\\1)"),ee=new RegExp("^([*_])"+K+"\\1(?!\\1)"),re=new RegExp("^(==)"+K+"\\1"),ne=new RegExp("^(~~)"+K+"\\1"),te=/^\\([^0-9A-Za-z\s])/,ie=/\\([^0-9A-Za-z\s])/g,ae=/^([\s\S](?:(?!  |[0-9]\.)[^=*_~\-\n<`\\\[!])*)/,oe=/^\n+/,le=/^([ \t]*)/,ce=/\\([^\\])/g,ue=/(?:^|\n)( *)$/,se="(?:\\d+\\.)",fe="(?:[*+-])";function de(e){return"( *)("+(1===e?se:fe)+") +"}var pe=de(1),he=de(2);function me(e){return new RegExp("^"+(1===e?pe:he))}var ge=me(1),ye=me(2);function ve(e){return new RegExp("^"+(1===e?pe:he)+"[^\\n]*(?:\\n(?!\\1"+(1===e?se:fe)+" )[^\\n]*)*(\\n|$)","gm")}var ke=ve(1),xe=ve(2);function be(e){var r=1===e?se:fe;return new RegExp("^( *)("+r+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+r+" (?!"+r+" ))\\n*|\\s*\\n*$)")}var Ce=be(1),Se=be(2);function we(e,r){var n=1===r,t=n?Ce:Se,i=n?ke:xe,a=n?ge:ye;return{match:je(function(e,r){var n=ue.exec(r.prevCapture);return n&&(r.list||!r.inline&&!r.simple)?t.exec(e=n[1]+e):null}),order:1,parse:function(e,r,t){var o=n?+e[2]:void 0,l=e[0].replace(p,"\n").match(i),c=!1;return{items:l.map(function(e,n){var i=a.exec(e)[0].length,o=new RegExp("^ {1,"+i+"}","gm"),u=e.replace(o,"").replace(a,""),s=n===l.length-1,f=-1!==u.indexOf("\n\n")||s&&c;c=f;var d,p=t.inline,h=t.list;t.list=!0,f?(t.inline=!1,d=Te(u)+"\n\n"):(t.inline=!0,d=Te(u));var m=r(d,t);return t.inline=p,t.list=h,m}),ordered:n,start:o}},render:function(r,n,t){return e(r.ordered?"ol":"ul",{key:t.key,start:r.type===o.orderedList?r.start:void 0},r.items.map(function(r,i){return e("li",{key:i},n(r,t))}))}}}var Ee=new RegExp("^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['\"]([\\s\\S]*?)['\"])?\\s*\\)"),Oe=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,ze=[h,k,x,T,B,A,H,Ce,Se],Le=[].concat(ze,[/^[^\n]+(?:  \n|\n{2,})/,M,$,I]);function Te(e){for(var r=e.length;r>0&&e[r-1]<=" ";)r--;return e.slice(0,r)}function Ae(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Be(e){return J.test(e)?"right":Q.test(e)?"center":V.test(e)?"left":null}function Me(e,r,n,t){var i=n.inTable;n.inTable=!0;var a=[[]],o="";function l(){if(o){var e=a[a.length-1];e.push.apply(e,r(o,n)),o=""}}return e.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach(function(e,r,n){"|"===e.trim()&&(l(),t)?0!==r&&r!==n.length-1&&a.push([]):o+=e}),l(),n.inTable=i,a}function Re(e,r,n){n.inline=!0;var t=e[2]?e[2].replace(X,"").split("|").map(Be):[],i=e[3]?function(e,r,n){return e.trim().split("\n").map(function(e){return Me(e,r,n,!0)})}(e[3],r,n):[],a=Me(e[1],r,n,!!i.length);return n.inline=!1,i.length?{align:t,cells:i,header:a,type:o.table}:{children:a,type:o.paragraph}}function $e(e,r){return null==e.align[r]?{}:{textAlign:e.align[r]}}function je(e){return e.inline=1,e}function Ie(e){return je(function(r,n){return n.inline?e.exec(r):null})}function De(e){return je(function(r,n){return n.inline||n.simple?e.exec(r):null})}function Ue(e){return function(r,n){return n.inline||n.simple?null:e.exec(r)}}function Ne(e){return je(function(r){return e.exec(r)})}function Pe(e,r){if(r.inline||r.simple)return null;var n="";e.split("\n").every(function(e){return e+="\n",!ze.some(function(r){return r.test(e)})&&(n+=e,!!e.trim())});var t=Te(n);return""==t?null:[n,,t]}var _e=/(javascript|vbscript|data(?!:image)):/i;function He(e){try{var r=decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"");if(_e.test(r))return null}catch(e){return null}return e}function Fe(e){return e.replace(ce,"$1")}function We(e,r,n){var t=n.inline||!1,i=n.simple||!1;n.inline=!0,n.simple=!0;var a=e(r,n);return n.inline=t,n.simple=i,a}function Ge(e,r,n){var t=n.inline||!1,i=n.simple||!1;n.inline=!1,n.simple=!0;var a=e(r,n);return n.inline=t,n.simple=i,a}function Ze(e,r,n){var t=n.inline||!1;n.inline=!1;var i=e(r,n);return n.inline=t,i}var qe=function(e,r,n){return{children:We(r,e[2],n)}};function Xe(){return{}}function Qe(){return null}function Ve(){return[].slice.call(arguments).filter(Boolean).join(" ")}function Je(e,r,n){for(var t=e,i=r.split(".");i.length&&void 0!==(t=t[i[0]]);)i.shift();return t||n}function Ke(e,r){var n=Je(r,e);return n?"function"==typeof n||"object"==typeof n&&"render"in n?n:Je(r,e+".component",e):e}function Ye(e,r){var i;function a(e,n){var i,a=Je(r.overrides,e+".props",{});return(i=r).createElement.apply(i,[Ke(e,r.overrides),t({},n,a,{className:Ve(null==n?void 0:n.className,a.className)||void 0})].concat([].slice.call(arguments,2)))}function p(e){e=e.replace(z,"");var n=!1;r.forceInline?n=!0:r.forceBlock||(n=!1===Z.test(e));for(var t=ue(ce(n?e:Te(e).replace(oe,"")+"\n\n",{inline:n}));"string"==typeof t[t.length-1]&&!t[t.length-1].trim();)t.pop();if(null===r.wrapper)return t;var i,o=r.wrapper||(n?"span":"div");if(t.length>1||r.forceWrapper)i=t;else{if(1===t.length)return"string"==typeof(i=t[0])?a("span",{key:"outer"},i):i;i=null}return r.createElement(o,{key:"outer"},i)}function X(e,n){var t=n.match(f);return t?t.reduce(function(n,t){var i=t.indexOf("=");if(-1!==i){var a=function(e){return-1!==e.indexOf("-")&&null===e.match(j)&&(e=e.replace(_,function(e,r){return r.toUpperCase()})),e}(t.slice(0,i)).trim(),o=function(e){var r=e[0];return('"'===r||"'"===r)&&e.length>=2&&e[e.length-1]===r?e.slice(1,-1):e}(t.slice(i+1).trim()),c=l[a]||a;if("ref"===c)return n;var u=n[c]=function(e,r,n,t){return"style"===r?function(e){var r=[],n="",t=!1,i=!1,a="";if(!e)return r;for(var o=0;o<e.length;o++){var l=e[o];if('"'!==l&&"'"!==l||t||(i?l===a&&(i=!1,a=""):(i=!0,a=l)),"("===l&&n.endsWith("url")?t=!0:")"===l&&t&&(t=!1),";"!==l||i||t)n+=l;else{var c=n.trim();if(c){var u=c.indexOf(":");if(u>0){var s=c.slice(0,u).trim(),f=c.slice(u+1).trim();r.push([s,f])}}n=""}}var d=n.trim();if(d){var p=d.indexOf(":");if(p>0){var h=d.slice(0,p).trim(),m=d.slice(p+1).trim();r.push([h,m])}}return r}(n).reduce(function(r,n){var i=n[0],a=n[1];return r[i.replace(/(-[a-z])/g,function(e){return e[1].toUpperCase()})]=t(a,e,i),r},{}):-1!==s.indexOf(r)?t(n,e,r):(n.match(D)&&(n=n.slice(1,n.length-1)),"true"===n||"false"!==n&&n)}(e,a,o,r.sanitizer);"string"==typeof u&&(M.test(u)||I.test(u))&&(n[c]=p(u.trim()))}else"style"!==t&&(n[l[t]||t]=!0);return n},{}):null}void 0===e&&(e=""),void 0===r&&(r={}),r.overrides=r.overrides||{},r.sanitizer=r.sanitizer||He,r.slugify=r.slugify||Ae,r.namedCodesToUnicode=r.namedCodesToUnicode?t({},c,r.namedCodesToUnicode):c,r.createElement=r.createElement||n.createElement;var Q=[],V={},J=((i={})[o.blockQuote]={match:Ue(h),order:1,parse:function(e,r,n){var t=e[0].replace(m,"").match(g);return{alert:t[1],children:r(t[2],n)}},render:function(e,n,t){var i={key:t.key};return e.alert&&(i.className="markdown-alert-"+r.slugify(e.alert.toLowerCase(),Ae),e.children.unshift({attrs:{},children:[{type:o.text,text:e.alert}],noInnerParse:!0,type:o.htmlBlock,tag:"header"})),a("blockquote",i,n(e.children,t))}},i[o.breakLine]={match:Ne(y),order:1,parse:Xe,render:function(e,r,n){return a("br",{key:n.key})}},i[o.breakThematic]={match:Ue(v),order:1,parse:Xe,render:function(e,r,n){return a("hr",{key:n.key})}},i[o.codeBlock]={match:Ue(x),order:0,parse:function(e){return{lang:void 0,text:Te(e[0].replace(/^ {4}/gm,"")).replace(ie,"$1")}},render:function(e,r,n){return a("pre",{key:n.key},a("code",t({},e.attrs,{className:e.lang?"lang-"+e.lang:""}),e.text))}},i[o.codeFenced]={match:Ue(k),order:0,parse:function(e){return{attrs:X("code",e[3]||""),lang:e[2]||void 0,text:e[4],type:o.codeBlock}}},i[o.codeInline]={match:De(b),order:3,parse:function(e){return{text:e[2].replace(ie,"$1")}},render:function(e,r,n){return a("code",{key:n.key},e.text)}},i[o.footnote]={match:Ue(w),order:0,parse:function(e){return Q.push({footnote:e[2],identifier:e[1]}),{}},render:Qe},i[o.footnoteReference]={match:Ie(E),order:1,parse:function(e){return{target:"#"+r.slugify(e[1],Ae),text:e[1]}},render:function(e,n,t){return a("a",{key:t.key,href:r.sanitizer(e.target,"a","href")},a("sup",{key:t.key},e.text))}},i[o.gfmTask]={match:Ie(L),order:1,parse:function(e){return{completed:"x"===e[1].toLowerCase()}},render:function(e,r,n){return a("input",{checked:e.completed,key:n.key,readOnly:!0,type:"checkbox"})}},i[o.heading]={match:Ue(r.enforceAtxHeadings?A:T),order:1,parse:function(e,n,t){return{children:We(n,e[2],t),id:r.slugify(e[2],Ae),level:e[1].length}},render:function(e,r,n){return a("h"+e.level,{id:e.id,key:n.key},r(e.children,n))}},i[o.headingSetext]={match:Ue(B),order:0,parse:function(e,r,n){return{children:We(r,e[1],n),level:"="===e[2]?1:2,type:o.heading}}},i[o.htmlBlock]={match:Ne(M),order:1,parse:function(e,r,n){var t,i=e[3].match(le),a=new RegExp("^"+i[1],"gm"),o=e[3].replace(a,""),l=(t=o,Le.some(function(e){return e.test(t)})?Ze:We),c=e[1].toLowerCase(),s=-1!==u.indexOf(c),f=(s?c:e[1]).trim(),d={attrs:X(f,e[2]),noInnerParse:s,tag:f};return n.inAnchor=n.inAnchor||"a"===c,s?d.text=e[3]:d.children=l(r,o,n),n.inAnchor=!1,d},render:function(e,r,n){return a(e.tag,t({key:n.key},e.attrs),e.text||(e.children?r(e.children,n):""))}},i[o.htmlSelfClosing]={match:Ne(I),order:1,parse:function(e){var r=e[1].trim();return{attrs:X(r,e[2]||""),tag:r}},render:function(e,r,n){return a(e.tag,t({},e.attrs,{key:n.key}))}},i[o.htmlComment]={match:Ne($),order:1,parse:function(){return{}},render:Qe},i[o.image]={match:De(Oe),order:1,parse:function(e){return{alt:e[1],target:Fe(e[2]),title:e[3]}},render:function(e,n,t){return a("img",{key:t.key,alt:e.alt||void 0,title:e.title||void 0,src:r.sanitizer(e.target,"img","src")})}},i[o.link]={match:Ie(Ee),order:3,parse:function(e,r,n){return{children:Ge(r,e[1],n),target:Fe(e[2]),title:e[3]}},render:function(e,n,t){return a("a",{key:t.key,href:r.sanitizer(e.target,"a","href"),title:e.title},n(e.children,t))}},i[o.linkAngleBraceStyleDetector]={match:Ie(P),order:0,parse:function(e){return{children:[{text:e[1],type:o.text}],target:e[1],type:o.link}}},i[o.linkBareUrlDetector]={match:je(function(e,n){return n.inAnchor||r.disableAutoLink?null:Ie(U)(e,n)}),order:0,parse:function(e){return{children:[{text:e[1],type:o.text}],target:e[1],title:void 0,type:o.link}}},i[o.linkMailtoDetector]={match:Ie(N),order:0,parse:function(e){var r=e[1],n=e[1];return d.test(n)||(n="mailto:"+n),{children:[{text:r.replace("mailto:",""),type:o.text}],target:n,type:o.link}}},i[o.orderedList]=we(a,1),i[o.unorderedList]=we(a,2),i[o.newlineCoalescer]={match:Ue(C),order:3,parse:Xe,render:function(){return"\n"}},i[o.paragraph]={match:je(Pe),order:3,parse:qe,render:function(e,r,n){return a("p",{key:n.key},r(e.children,n))}},i[o.ref]={match:Ie(F),order:0,parse:function(e){return V[e[1]]={target:e[2],title:e[4]},{}},render:Qe},i[o.refImage]={match:De(W),order:0,parse:function(e){return{alt:e[1]||void 0,ref:e[2]}},render:function(e,n,t){return V[e.ref]?a("img",{key:t.key,alt:e.alt,src:r.sanitizer(V[e.ref].target,"img","src"),title:V[e.ref].title}):null}},i[o.refLink]={match:Ie(G),order:0,parse:function(e,r,n){return{children:r(e[1],n),fallbackChildren:e[0],ref:e[2]}},render:function(e,n,t){return V[e.ref]?a("a",{key:t.key,href:r.sanitizer(V[e.ref].target,"a","href"),title:V[e.ref].title},n(e.children,t)):a("span",{key:t.key},e.fallbackChildren)}},i[o.table]={match:Ue(H),order:1,parse:Re,render:function(e,r,n){var t=e;return a("table",{key:n.key},a("thead",null,a("tr",null,t.header.map(function(e,i){return a("th",{key:i,style:$e(t,i)},r(e,n))}))),a("tbody",null,t.cells.map(function(e,i){return a("tr",{key:i},e.map(function(e,i){return a("td",{key:i,style:$e(t,i)},r(e,n))}))})))}},i[o.text]={match:Ne(ae),order:4,parse:function(e){return{text:e[0].replace(R,function(e,n){return r.namedCodesToUnicode[n]?r.namedCodesToUnicode[n]:e})}},render:function(e){return e.text}},i[o.textBolded]={match:De(Y),order:2,parse:function(e,r,n){return{children:r(e[2],n)}},render:function(e,r,n){return a("strong",{key:n.key},r(e.children,n))}},i[o.textEmphasized]={match:De(ee),order:3,parse:function(e,r,n){return{children:r(e[2],n)}},render:function(e,r,n){return a("em",{key:n.key},r(e.children,n))}},i[o.textEscaped]={match:De(te),order:1,parse:function(e){return{text:e[1],type:o.text}}},i[o.textMarked]={match:De(re),order:3,parse:qe,render:function(e,r,n){return a("mark",{key:n.key},r(e.children,n))}},i[o.textStrikethroughed]={match:De(ne),order:3,parse:qe,render:function(e,r,n){return a("del",{key:n.key},r(e.children,n))}},i);!0===r.disableParsingRawHTML&&(delete J[o.htmlBlock],delete J[o.htmlSelfClosing]);var K,ce=function(e){var r=Object.keys(e);function n(t,i){var a,o,l=[],c="",u="";for(i.prevCapture=i.prevCapture||"";t;)for(var s=0;s<r.length;)if(a=e[c=r[s]],!i.inline||a.match.inline){var f=a.match(t,i);if(f){i.prevCapture+=u=f[0],t=t.substring(u.length),null==(o=a.parse(f,n,i)).type&&(o.type=c),l.push(o);break}s++}else s++;return i.prevCapture="",l}return r.sort(function(r,n){var t=e[r].order,i=e[n].order;return t!==i?t-i:r<n?-1:1}),function(e,r){return n(function(e){return e.replace(S,"\n").replace(O,"").replace(q,"    ")}(e),r)}}(J),ue=(K=function(e,r){return function(n,t,i){var a=e[n.type].render;return r?r(function(){return a(n,t,i)},n,t,i):a(n,t,i)}}(J,r.renderRule),function e(r,n){if(void 0===n&&(n={}),Array.isArray(r)){for(var t=n.key,i=[],a=!1,o=0;o<r.length;o++){n.key=o;var l=e(r[o],n),c="string"==typeof l;c&&a?i[i.length-1]+=l:null!==l&&i.push(l),a=c}return n.key=t,i}return K(r,e,n)}),se=p(e);return Q.length?a("div",null,se,a("footer",{key:"footer"},Q.map(function(e){return a("div",{id:r.slugify(e.identifier,Ae),key:e.identifier},e.identifier,ue(ce(e.footnote,{inline:!0})))}))):se}var er=function(e){var r=e.children,t=void 0===r?"":r,i=e.options,o=function(e,r){if(null==e)return{};var n,t,i={},a=Object.keys(e);for(t=0;t<a.length;t++)r.indexOf(n=a[t])>=0||(i[n]=e[n]);return i}(e,a);return n.cloneElement(Ye(t,i),o)};return Object.assign(er,{compiler:Ye,RuleType:o}),er});
//# sourceMappingURL=index.umd.js.map
