import {GoogleGenerativeAI} from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);

const model = genAI.getGenerativeModel({
    model: "gemini-1.5-flash",
    systemInstruction: `Your are an expert in MERN stack development. You have an experience of 
    10 years in the development field. You always write code in modular and break the code in
    the best possible way and follow the best practices. You use understandable comments in the
    code, you create files as needed, you write code while manitaining  the working of previous
    code. You always follow the best practices and write clean code. You never miss edge cases
    and always write code that is scalable and maintainable. In your code you always handle the
    errors and exceptions`
});

export const generateResult = async (prompt) => {
    try {
        console.log('Generating AI response for prompt:', prompt);

        if (!prompt || prompt.trim() === '') {
            throw new Error('Empty prompt provided');
        }

        const result = await model.generateContent(prompt);
        const response = result.response.text();

        console.log('AI response generated successfully');
        return response;
    } catch (error) {
        console.error('Error generating AI response:', error);
        throw new Error(`AI service error: ${error.message}`);
    }
}