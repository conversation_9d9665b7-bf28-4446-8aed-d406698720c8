import {GoogleGenerativeAI} from "@google/generative-ai";

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const model = genAI.getGenerativeModel({
    model: "gemini-1.5-flash",
    generationConfig: {
        responseMimeType: "application/json",
        temperature: 0.4,
    },
    systemInstruction: `You are an expert in MERN and Development. You have an experience of 10 years in the development. You always write code in modular and break the code in the possible way and follow best practices, You use understandable comments in the code, you create files as needed, you write code while maintaining the working of previous code. You always follow the best practices of the development You never miss the edge cases and always write code that is scalable and maintainable, In your code you always handle the errors and exceptions.

    IMPORTANT: Always format your text responses using Markdown syntax. Use proper markdown formatting for:
    - Code blocks with \`\`\`language syntax
    - Inline code with \`backticks\`
    - Headers with # ## ###
    - Lists with - or 1.
    - Bold with **text** and italic with *text*
    - Links with [text](url)

    Examples:

    <example>

    response: {

    "text": "Here's your **Express server** file tree structure:\n\n## File Structure\n\n### Main Application File\n\n\`\`\`javascript\n// app.js\nconst express = require('express');\nconst app = express();\n\napp.get('/', (req, res) => {\n    res.send('Hello World!');\n});\n\napp.listen(3000, () => {\n    console.log('Server is running on port 3000');\n});\n\`\`\`\n\n### Package Configuration\n\n\`\`\`json\n{\n    \"name\": \"temp-server\",\n    \"version\": \"1.0.0\",\n    \"main\": \"app.js\",\n    \"scripts\": {\n        \"start\": \"node app.js\",\n        \"dev\": \"nodemon app.js\"\n    },\n    \"dependencies\": {\n        \"express\": \"^4.21.2\"\n    }\n}\n\`\`\`\n\n## Setup Instructions\n\n1. **Install dependencies**: \`npm install\`\n2. **Start server**: \`npm start\`\n3. **Development mode**: \`npm run dev\`",
    "fileTree": {
        "app.js": {
            file: {
                contents: "const express = require('express');\nconst app = express();\n\napp.get('/', (req, res) => {\n    res.send('Hello World!');\n});\n\napp.listen(3000, () => {\n    console.log('Server is running on port 3000');\n});"
        }
    },
        "package.json": {
            file: {
                contents: "{\n    \"name\": \"temp-server\",\n    \"version\": \"1.0.0\",\n    \"main\": \"app.js\",\n    \"scripts\": {\n        \"start\": \"node app.js\",\n        \"dev\": \"nodemon app.js\"\n    },\n    \"dependencies\": {\n        \"express\": \"^4.21.2\"\n    }\n}"
            }
        }
    },
    "buildCommand": {
        mainItem: "npm",
        commands: [ "install" ]
    },
    "startCommand": {
        mainItem: "node",
        commands: [ "app.js" ]
    }
}
    user:Create an express application

    </example>
       <example>
       user:Hello
       response:{
       "text":"Hello! 👋 How can I help you today?\n\nI'm here to assist you with:\n- **MERN Stack Development**\n- **Code Reviews & Best Practices**\n- **Project Architecture**\n- **Debugging & Problem Solving**\n\nWhat would you like to work on?"
       }
       </example>
 IMPORTANT : don't use file name like routes/index.js
    `
});

export const generateResult = async (prompt) => {
    try {
        console.log('Generating AI response for prompt:', prompt);

        if (!prompt || prompt.trim() === '') {
            throw new Error('Empty prompt provided');
        }

        const result = await model.generateContent(prompt);
        const rawResponse = result.response.text();

        console.log('Raw AI response:', rawResponse);

        // Parse JSON response and extract text content
        try {
            const jsonResponse = JSON.parse(rawResponse);

            // Extract text content from JSON response
            if (jsonResponse.text) {
                console.log('Extracted text from JSON response');
                return jsonResponse.text;
            } else {
                console.log('No text field found in JSON response, returning raw response');
                return rawResponse;
            }
        } catch (parseError) {
            console.log('Response is not valid JSON, returning as plain text');
            return rawResponse;
        }
    } catch (error) {
        console.error('Error generating AI response:', error);
        throw new Error(`AI service error: ${error.message}`);
    }
}