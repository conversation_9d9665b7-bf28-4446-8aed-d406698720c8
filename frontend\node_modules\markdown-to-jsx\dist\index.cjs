function e(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach(function(n){if("default"!==n){var t=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,t.get?t:{enumerable:!0,get:function(){return e[n]}})}}),r.default=e,r}var r=/*#__PURE__*/e(require("react"));function n(){return n=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e},n.apply(this,arguments)}var t,i=["children","options"],a={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"};!function(e){e[e.MAX=0]="MAX",e[e.HIGH=1]="HIGH",e[e.MED=2]="MED",e[e.LOW=3]="LOW",e[e.MIN=4]="MIN"}(t||(t={}));var o=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce(function(e,r){return e[r.toLowerCase()]=r,e},{class:"className",for:"htmlFor"}),l={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},c=["style","script"],u=["src","href","data","formAction","srcDoc","action"],s=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,f=/mailto:/i,d=/\n{2,}$/,p=/^(\s*>[\s\S]*?)(?=\n\n|$)/,h=/^ *> ?/gm,m=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,g=/^ {2,}\n/,y=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,v=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,k=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,x=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,b=/^(?:\n *)*\n/,C=/\r\n?/g,S=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,w=/^\[\^([^\]]+)]/,E=/\f/g,O=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,z=/^\s*?\[(x|\s)\]/,L=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,A=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,T=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,B=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,$=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,M=/^<!--[\s\S]*?(?:-->)/,R=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,I=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,j=/^\{.*\}$/,D=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,U=/^<([^ >]+@[^ >]+)>/,N=/^<([^ >]+:\/[^ >]+)>/,P=/-([a-z])?/gi,_=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,H=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,F=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,W=/^\[([^\]]*)\] ?\[([^\]]*)\]/,G=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,Z=/\t/g,q=/(^ *\||\| *$)/g,Q=/^ *:-+: *$/,V=/^ *:-+ *$/,X=/^ *-+: *$/,J="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",K=new RegExp("^([*_])\\1"+J+"\\1\\1(?!\\1)"),Y=new RegExp("^([*_])"+J+"\\1(?!\\1)"),ee=new RegExp("^(==)"+J+"\\1"),re=new RegExp("^(~~)"+J+"\\1"),ne=/^\\([^0-9A-Za-z\s])/,te=/\\([^0-9A-Za-z\s])/g,ie=/^([\s\S](?:(?!  |[0-9]\.)[^=*_~\-\n<`\\\[!])*)/,ae=/^\n+/,oe=/^([ \t]*)/,le=/\\([^\\])/g,ce=/(?:^|\n)( *)$/,ue="(?:\\d+\\.)",se="(?:[*+-])";function fe(e){return"( *)("+(1===e?ue:se)+") +"}var de=fe(1),pe=fe(2);function he(e){return new RegExp("^"+(1===e?de:pe))}var me=he(1),ge=he(2);function ye(e){return new RegExp("^"+(1===e?de:pe)+"[^\\n]*(?:\\n(?!\\1"+(1===e?ue:se)+" )[^\\n]*)*(\\n|$)","gm")}var ve=ye(1),ke=ye(2);function xe(e){var r=1===e?ue:se;return new RegExp("^( *)("+r+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+r+" (?!"+r+" ))\\n*|\\s*\\n*$)")}var be=xe(1),Ce=xe(2);function Se(e,r){var n=1===r,t=n?be:Ce,i=n?ve:ke,o=n?me:ge;return{match:Re(function(e,r){var n=ce.exec(r.prevCapture);return n&&(r.list||!r.inline&&!r.simple)?t.exec(e=n[1]+e):null}),order:1,parse:function(e,r,t){var a=n?+e[2]:void 0,l=e[0].replace(d,"\n").match(i),c=!1;return{items:l.map(function(e,n){var i=o.exec(e)[0].length,a=new RegExp("^ {1,"+i+"}","gm"),u=e.replace(a,"").replace(o,""),s=n===l.length-1,f=-1!==u.indexOf("\n\n")||s&&c;c=f;var d,p=t.inline,h=t.list;t.list=!0,f?(t.inline=!1,d=Le(u)+"\n\n"):(t.inline=!0,d=Le(u));var m=r(d,t);return t.inline=p,t.list=h,m}),ordered:n,start:a}},render:function(r,n,t){return e(r.ordered?"ol":"ul",{key:t.key,start:r.type===a.orderedList?r.start:void 0},r.items.map(function(r,i){return e("li",{key:i},n(r,t))}))}}}var we=new RegExp("^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['\"]([\\s\\S]*?)['\"])?\\s*\\)"),Ee=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,Oe=[p,v,k,L,T,A,_,be,Ce],ze=[].concat(Oe,[/^[^\n]+(?:  \n|\n{2,})/,B,M,I]);function Le(e){for(var r=e.length;r>0&&e[r-1]<=" ";)r--;return e.slice(0,r)}function Ae(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Te(e){return X.test(e)?"right":Q.test(e)?"center":V.test(e)?"left":null}function Be(e,r,n,t){var i=n.inTable;n.inTable=!0;var a=[[]],o="";function l(){if(o){var e=a[a.length-1];e.push.apply(e,r(o,n)),o=""}}return e.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach(function(e,r,n){"|"===e.trim()&&(l(),t)?0!==r&&r!==n.length-1&&a.push([]):o+=e}),l(),n.inTable=i,a}function $e(e,r,n){n.inline=!0;var t=e[2]?e[2].replace(q,"").split("|").map(Te):[],i=e[3]?function(e,r,n){return e.trim().split("\n").map(function(e){return Be(e,r,n,!0)})}(e[3],r,n):[],o=Be(e[1],r,n,!!i.length);return n.inline=!1,i.length?{align:t,cells:i,header:o,type:a.table}:{children:o,type:a.paragraph}}function Me(e,r){return null==e.align[r]?{}:{textAlign:e.align[r]}}function Re(e){return e.inline=1,e}function Ie(e){return Re(function(r,n){return n.inline?e.exec(r):null})}function je(e){return Re(function(r,n){return n.inline||n.simple?e.exec(r):null})}function De(e){return function(r,n){return n.inline||n.simple?null:e.exec(r)}}function Ue(e){return Re(function(r){return e.exec(r)})}function Ne(e,r){if(r.inline||r.simple)return null;var n="";e.split("\n").every(function(e){return e+="\n",!Oe.some(function(r){return r.test(e)})&&(n+=e,!!e.trim())});var t=Le(n);return""==t?null:[n,,t]}var Pe=/(javascript|vbscript|data(?!:image)):/i;function _e(e){try{var r=decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"");if(Pe.test(r))return null}catch(e){return null}return e}function He(e){return e.replace(le,"$1")}function Fe(e,r,n){var t=n.inline||!1,i=n.simple||!1;n.inline=!0,n.simple=!0;var a=e(r,n);return n.inline=t,n.simple=i,a}function We(e,r,n){var t=n.inline||!1,i=n.simple||!1;n.inline=!1,n.simple=!0;var a=e(r,n);return n.inline=t,n.simple=i,a}function Ge(e,r,n){var t=n.inline||!1;n.inline=!1;var i=e(r,n);return n.inline=t,i}var Ze=function(e,r,n){return{children:Fe(r,e[2],n)}};function qe(){return{}}function Qe(){return null}function Ve(){return[].slice.call(arguments).filter(Boolean).join(" ")}function Xe(e,r,n){for(var t=e,i=r.split(".");i.length&&void 0!==(t=t[i[0]]);)i.shift();return t||n}function Je(e,r){var n=Xe(r,e);return n?"function"==typeof n||"object"==typeof n&&"render"in n?n:Xe(r,e+".component",e):e}function Ke(e,t){var i;function d(e,r){var i,a=Xe(t.overrides,e+".props",{});return(i=t).createElement.apply(i,[Je(e,t.overrides),n({},r,a,{className:Ve(null==r?void 0:r.className,a.className)||void 0})].concat([].slice.call(arguments,2)))}function q(e){e=e.replace(O,"");var r=!1;t.forceInline?r=!0:t.forceBlock||(r=!1===G.test(e));for(var n=ue(ce(r?e:Le(e).replace(ae,"")+"\n\n",{inline:r}));"string"==typeof n[n.length-1]&&!n[n.length-1].trim();)n.pop();if(null===t.wrapper)return n;var i,a=t.wrapper||(r?"span":"div");if(n.length>1||t.forceWrapper)i=n;else{if(1===n.length)return"string"==typeof(i=n[0])?d("span",{key:"outer"},i):i;i=null}return t.createElement(a,{key:"outer"},i)}function Q(e,r){var n=r.match(s);return n?n.reduce(function(r,n){var i=n.indexOf("=");if(-1!==i){var a=function(e){return-1!==e.indexOf("-")&&null===e.match(R)&&(e=e.replace(P,function(e,r){return r.toUpperCase()})),e}(n.slice(0,i)).trim(),l=function(e){var r=e[0];return('"'===r||"'"===r)&&e.length>=2&&e[e.length-1]===r?e.slice(1,-1):e}(n.slice(i+1).trim()),c=o[a]||a;if("ref"===c)return r;var s=r[c]=function(e,r,n,t){return"style"===r?function(e){var r=[],n="",t=!1,i=!1,a="";if(!e)return r;for(var o=0;o<e.length;o++){var l=e[o];if('"'!==l&&"'"!==l||t||(i?l===a&&(i=!1,a=""):(i=!0,a=l)),"("===l&&n.endsWith("url")?t=!0:")"===l&&t&&(t=!1),";"!==l||i||t)n+=l;else{var c=n.trim();if(c){var u=c.indexOf(":");if(u>0){var s=c.slice(0,u).trim(),f=c.slice(u+1).trim();r.push([s,f])}}n=""}}var d=n.trim();if(d){var p=d.indexOf(":");if(p>0){var h=d.slice(0,p).trim(),m=d.slice(p+1).trim();r.push([h,m])}}return r}(n).reduce(function(r,n){var i=n[0],a=n[1];return r[i.replace(/(-[a-z])/g,function(e){return e[1].toUpperCase()})]=t(a,e,i),r},{}):-1!==u.indexOf(r)?t(n,e,r):(n.match(j)&&(n=n.slice(1,n.length-1)),"true"===n||"false"!==n&&n)}(e,a,l,t.sanitizer);"string"==typeof s&&(B.test(s)||I.test(s))&&(r[c]=q(s.trim()))}else"style"!==n&&(r[o[n]||n]=!0);return r},{}):null}void 0===e&&(e=""),void 0===t&&(t={}),t.overrides=t.overrides||{},t.sanitizer=t.sanitizer||_e,t.slugify=t.slugify||Ae,t.namedCodesToUnicode=t.namedCodesToUnicode?n({},l,t.namedCodesToUnicode):l,t.createElement=t.createElement||r.createElement;var V=[],X={},J=((i={})[a.blockQuote]={match:De(p),order:1,parse:function(e,r,n){var t=e[0].replace(h,"").match(m);return{alert:t[1],children:r(t[2],n)}},render:function(e,r,n){var i={key:n.key};return e.alert&&(i.className="markdown-alert-"+t.slugify(e.alert.toLowerCase(),Ae),e.children.unshift({attrs:{},children:[{type:a.text,text:e.alert}],noInnerParse:!0,type:a.htmlBlock,tag:"header"})),d("blockquote",i,r(e.children,n))}},i[a.breakLine]={match:Ue(g),order:1,parse:qe,render:function(e,r,n){return d("br",{key:n.key})}},i[a.breakThematic]={match:De(y),order:1,parse:qe,render:function(e,r,n){return d("hr",{key:n.key})}},i[a.codeBlock]={match:De(k),order:0,parse:function(e){return{lang:void 0,text:Le(e[0].replace(/^ {4}/gm,"")).replace(te,"$1")}},render:function(e,r,t){return d("pre",{key:t.key},d("code",n({},e.attrs,{className:e.lang?"lang-"+e.lang:""}),e.text))}},i[a.codeFenced]={match:De(v),order:0,parse:function(e){return{attrs:Q("code",e[3]||""),lang:e[2]||void 0,text:e[4],type:a.codeBlock}}},i[a.codeInline]={match:je(x),order:3,parse:function(e){return{text:e[2].replace(te,"$1")}},render:function(e,r,n){return d("code",{key:n.key},e.text)}},i[a.footnote]={match:De(S),order:0,parse:function(e){return V.push({footnote:e[2],identifier:e[1]}),{}},render:Qe},i[a.footnoteReference]={match:Ie(w),order:1,parse:function(e){return{target:"#"+t.slugify(e[1],Ae),text:e[1]}},render:function(e,r,n){return d("a",{key:n.key,href:t.sanitizer(e.target,"a","href")},d("sup",{key:n.key},e.text))}},i[a.gfmTask]={match:Ie(z),order:1,parse:function(e){return{completed:"x"===e[1].toLowerCase()}},render:function(e,r,n){return d("input",{checked:e.completed,key:n.key,readOnly:!0,type:"checkbox"})}},i[a.heading]={match:De(t.enforceAtxHeadings?A:L),order:1,parse:function(e,r,n){return{children:Fe(r,e[2],n),id:t.slugify(e[2],Ae),level:e[1].length}},render:function(e,r,n){return d("h"+e.level,{id:e.id,key:n.key},r(e.children,n))}},i[a.headingSetext]={match:De(T),order:0,parse:function(e,r,n){return{children:Fe(r,e[1],n),level:"="===e[2]?1:2,type:a.heading}}},i[a.htmlBlock]={match:Ue(B),order:1,parse:function(e,r,n){var t,i=e[3].match(oe),a=new RegExp("^"+i[1],"gm"),o=e[3].replace(a,""),l=(t=o,ze.some(function(e){return e.test(t)})?Ge:Fe),u=e[1].toLowerCase(),s=-1!==c.indexOf(u),f=(s?u:e[1]).trim(),d={attrs:Q(f,e[2]),noInnerParse:s,tag:f};return n.inAnchor=n.inAnchor||"a"===u,s?d.text=e[3]:d.children=l(r,o,n),n.inAnchor=!1,d},render:function(e,r,t){return d(e.tag,n({key:t.key},e.attrs),e.text||(e.children?r(e.children,t):""))}},i[a.htmlSelfClosing]={match:Ue(I),order:1,parse:function(e){var r=e[1].trim();return{attrs:Q(r,e[2]||""),tag:r}},render:function(e,r,t){return d(e.tag,n({},e.attrs,{key:t.key}))}},i[a.htmlComment]={match:Ue(M),order:1,parse:function(){return{}},render:Qe},i[a.image]={match:je(Ee),order:1,parse:function(e){return{alt:e[1],target:He(e[2]),title:e[3]}},render:function(e,r,n){return d("img",{key:n.key,alt:e.alt||void 0,title:e.title||void 0,src:t.sanitizer(e.target,"img","src")})}},i[a.link]={match:Ie(we),order:3,parse:function(e,r,n){return{children:We(r,e[1],n),target:He(e[2]),title:e[3]}},render:function(e,r,n){return d("a",{key:n.key,href:t.sanitizer(e.target,"a","href"),title:e.title},r(e.children,n))}},i[a.linkAngleBraceStyleDetector]={match:Ie(N),order:0,parse:function(e){return{children:[{text:e[1],type:a.text}],target:e[1],type:a.link}}},i[a.linkBareUrlDetector]={match:Re(function(e,r){return r.inAnchor||t.disableAutoLink?null:Ie(D)(e,r)}),order:0,parse:function(e){return{children:[{text:e[1],type:a.text}],target:e[1],title:void 0,type:a.link}}},i[a.linkMailtoDetector]={match:Ie(U),order:0,parse:function(e){var r=e[1],n=e[1];return f.test(n)||(n="mailto:"+n),{children:[{text:r.replace("mailto:",""),type:a.text}],target:n,type:a.link}}},i[a.orderedList]=Se(d,1),i[a.unorderedList]=Se(d,2),i[a.newlineCoalescer]={match:De(b),order:3,parse:qe,render:function(){return"\n"}},i[a.paragraph]={match:Re(Ne),order:3,parse:Ze,render:function(e,r,n){return d("p",{key:n.key},r(e.children,n))}},i[a.ref]={match:Ie(H),order:0,parse:function(e){return X[e[1]]={target:e[2],title:e[4]},{}},render:Qe},i[a.refImage]={match:je(F),order:0,parse:function(e){return{alt:e[1]||void 0,ref:e[2]}},render:function(e,r,n){return X[e.ref]?d("img",{key:n.key,alt:e.alt,src:t.sanitizer(X[e.ref].target,"img","src"),title:X[e.ref].title}):null}},i[a.refLink]={match:Ie(W),order:0,parse:function(e,r,n){return{children:r(e[1],n),fallbackChildren:e[0],ref:e[2]}},render:function(e,r,n){return X[e.ref]?d("a",{key:n.key,href:t.sanitizer(X[e.ref].target,"a","href"),title:X[e.ref].title},r(e.children,n)):d("span",{key:n.key},e.fallbackChildren)}},i[a.table]={match:De(_),order:1,parse:$e,render:function(e,r,n){var t=e;return d("table",{key:n.key},d("thead",null,d("tr",null,t.header.map(function(e,i){return d("th",{key:i,style:Me(t,i)},r(e,n))}))),d("tbody",null,t.cells.map(function(e,i){return d("tr",{key:i},e.map(function(e,i){return d("td",{key:i,style:Me(t,i)},r(e,n))}))})))}},i[a.text]={match:Ue(ie),order:4,parse:function(e){return{text:e[0].replace($,function(e,r){return t.namedCodesToUnicode[r]?t.namedCodesToUnicode[r]:e})}},render:function(e){return e.text}},i[a.textBolded]={match:je(K),order:2,parse:function(e,r,n){return{children:r(e[2],n)}},render:function(e,r,n){return d("strong",{key:n.key},r(e.children,n))}},i[a.textEmphasized]={match:je(Y),order:3,parse:function(e,r,n){return{children:r(e[2],n)}},render:function(e,r,n){return d("em",{key:n.key},r(e.children,n))}},i[a.textEscaped]={match:je(ne),order:1,parse:function(e){return{text:e[1],type:a.text}}},i[a.textMarked]={match:je(ee),order:3,parse:Ze,render:function(e,r,n){return d("mark",{key:n.key},r(e.children,n))}},i[a.textStrikethroughed]={match:je(re),order:3,parse:Ze,render:function(e,r,n){return d("del",{key:n.key},r(e.children,n))}},i);!0===t.disableParsingRawHTML&&(delete J[a.htmlBlock],delete J[a.htmlSelfClosing]);var le,ce=function(e){var r=Object.keys(e);function n(t,i){var a,o,l=[],c="",u="";for(i.prevCapture=i.prevCapture||"";t;)for(var s=0;s<r.length;)if(a=e[c=r[s]],!i.inline||a.match.inline){var f=a.match(t,i);if(f){i.prevCapture+=u=f[0],t=t.substring(u.length),null==(o=a.parse(f,n,i)).type&&(o.type=c),l.push(o);break}s++}else s++;return i.prevCapture="",l}return r.sort(function(r,n){var t=e[r].order,i=e[n].order;return t!==i?t-i:r<n?-1:1}),function(e,r){return n(function(e){return e.replace(C,"\n").replace(E,"").replace(Z,"    ")}(e),r)}}(J),ue=(le=function(e,r){return function(n,t,i){var a=e[n.type].render;return r?r(function(){return a(n,t,i)},n,t,i):a(n,t,i)}}(J,t.renderRule),function e(r,n){if(void 0===n&&(n={}),Array.isArray(r)){for(var t=n.key,i=[],a=!1,o=0;o<r.length;o++){n.key=o;var l=e(r[o],n),c="string"==typeof l;c&&a?i[i.length-1]+=l:null!==l&&i.push(l),a=c}return n.key=t,i}return le(r,e,n)}),se=q(e);return V.length?d("div",null,se,d("footer",{key:"footer"},V.map(function(e){return d("div",{id:t.slugify(e.identifier,Ae),key:e.identifier},e.identifier,ue(ce(e.footnote,{inline:!0})))}))):se}var Ye=function(e){var n=e.children,t=void 0===n?"":n,a=e.options,o=function(e,r){if(null==e)return{};var n,t,i={},a=Object.keys(e);for(t=0;t<a.length;t++)r.indexOf(n=a[t])>=0||(i[n]=e[n]);return i}(e,i);return r.cloneElement(Ke(t,a),o)};Object.assign(Ye,{compiler:Ke,RuleType:a}),module.exports=Ye;
//# sourceMappingURL=index.cjs.map
