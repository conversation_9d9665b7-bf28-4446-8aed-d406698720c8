import {
  require_react
} from "./chunk-UGC3UZ7L.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/markdown-to-jsx/dist/index.modern.js
var e = __toESM(require_react());
function t() {
  return t = Object.assign ? Object.assign.bind() : function(e2) {
    for (var t2 = 1; t2 < arguments.length; t2++) {
      var n2 = arguments[t2];
      for (var r2 in n2) Object.prototype.hasOwnProperty.call(n2, r2) && (e2[r2] = n2[r2]);
    }
    return e2;
  }, t.apply(this, arguments);
}
var n = ["children", "options"];
var r = { blockQuote: "0", breakLine: "1", breakThematic: "2", codeBlock: "3", codeFenced: "4", codeInline: "5", footnote: "6", footnoteReference: "7", gfmTask: "8", heading: "9", headingSetext: "10", htmlBlock: "11", htmlComment: "12", htmlSelfClosing: "13", image: "14", link: "15", linkAngleBraceStyleDetector: "16", linkBareUrlDetector: "17", linkMailtoDetector: "18", newlineCoalescer: "19", orderedList: "20", paragraph: "21", ref: "22", refImage: "23", refLink: "24", table: "25", tableSeparator: "26", text: "27", textBolded: "28", textEmphasized: "29", textEscaped: "30", textMarked: "31", textStrikethroughed: "32", unorderedList: "33" };
var i;
!function(e2) {
  e2[e2.MAX = 0] = "MAX", e2[e2.HIGH = 1] = "HIGH", e2[e2.MED = 2] = "MED", e2[e2.LOW = 3] = "LOW", e2[e2.MIN = 4] = "MIN";
}(i || (i = {}));
var l = ["allowFullScreen", "allowTransparency", "autoComplete", "autoFocus", "autoPlay", "cellPadding", "cellSpacing", "charSet", "classId", "colSpan", "contentEditable", "contextMenu", "crossOrigin", "encType", "formAction", "formEncType", "formMethod", "formNoValidate", "formTarget", "frameBorder", "hrefLang", "inputMode", "keyParams", "keyType", "marginHeight", "marginWidth", "maxLength", "mediaGroup", "minLength", "noValidate", "radioGroup", "readOnly", "rowSpan", "spellCheck", "srcDoc", "srcLang", "srcSet", "tabIndex", "useMap"].reduce((e2, t2) => (e2[t2.toLowerCase()] = t2, e2), { class: "className", for: "htmlFor" });
var o = { amp: "&", apos: "'", gt: ">", lt: "<", nbsp: " ", quot: "“" };
var a = ["style", "script"];
var c = ["src", "href", "data", "formAction", "srcDoc", "action"];
var s = /([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi;
var d = /mailto:/i;
var u = /\n{2,}$/;
var p = /^(\s*>[\s\S]*?)(?=\n\n|$)/;
var f = /^ *> ?/gm;
var h = /^(?:\[!([^\]]*)\]\n)?([\s\S]*)/;
var m = /^ {2,}\n/;
var g = /^(?:( *[-*_])){3,} *(?:\n *)+\n/;
var y = /^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/;
var k = /^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/;
var x = /^(`+)((?:\\`|(?!\1)`|[^`])+)\1/;
var b = /^(?:\n *)*\n/;
var v = /\r\n?/g;
var C = /^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/;
var $ = /^\[\^([^\]]+)]/;
var S = /\f/g;
var w = /^---[ \t]*\n(.|\n)*\n---[ \t]*\n/;
var E = /^\s*?\[(x|\s)\]/;
var z = /^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/;
var L = /^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/;
var A = /^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/;
var O = /^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i;
var T = /&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi;
var B = /^<!--[\s\S]*?(?:-->)/;
var M = /^(data|aria|x)-[a-z_][a-z\d_.-]*$/;
var R = /^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i;
var I = /^\{.*\}$/;
var D = /^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/;
var U = /^<([^ >]+@[^ >]+)>/;
var N = /^<([^ >]+:\/[^ >]+)>/;
var j = /-([a-z])?/gi;
var H = /^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/;
var P = /^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/;
var _ = /^!\[([^\]]*)\] ?\[([^\]]*)\]/;
var F = /^\[([^\]]*)\] ?\[([^\]]*)\]/;
var W = /(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/;
var G = /\t/g;
var Z = /(^ *\||\| *$)/g;
var q = /^ *:-+: *$/;
var Q = /^ *:-+ *$/;
var V = /^ *-+: *$/;
var X = "((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)";
var J = new RegExp(`^([*_])\\1${X}\\1\\1(?!\\1)`);
var K = new RegExp(`^([*_])${X}\\1(?!\\1)`);
var Y = new RegExp(`^(==)${X}\\1`);
var ee = new RegExp(`^(~~)${X}\\1`);
var te = /^\\([^0-9A-Za-z\s])/;
var ne = /\\([^0-9A-Za-z\s])/g;
var re = /^([\s\S](?:(?!  |[0-9]\.)[^=*_~\-\n<`\\\[!])*)/;
var ie = /^\n+/;
var le = /^([ \t]*)/;
var oe = /\\([^\\])/g;
var ae = /(?:^|\n)( *)$/;
var ce = "(?:\\d+\\.)";
var se = "(?:[*+-])";
function de(e2) {
  return "( *)(" + (1 === e2 ? ce : se) + ") +";
}
var ue = de(1);
var pe = de(2);
function fe(e2) {
  return new RegExp("^" + (1 === e2 ? ue : pe));
}
var he = fe(1);
var me = fe(2);
function ge(e2) {
  return new RegExp("^" + (1 === e2 ? ue : pe) + "[^\\n]*(?:\\n(?!\\1" + (1 === e2 ? ce : se) + " )[^\\n]*)*(\\n|$)", "gm");
}
var ye = ge(1);
var ke = ge(2);
function xe(e2) {
  const t2 = 1 === e2 ? ce : se;
  return new RegExp("^( *)(" + t2 + ") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1" + t2 + " (?!" + t2 + " ))\\n*|\\s*\\n*$)");
}
var be = xe(1);
var ve = xe(2);
function Ce(e2, t2) {
  const n2 = 1 === t2, i2 = n2 ? be : ve, l2 = n2 ? ye : ke, o2 = n2 ? he : me;
  return { match: Me(function(e3, t3) {
    const n3 = ae.exec(t3.prevCapture);
    return n3 && (t3.list || !t3.inline && !t3.simple) ? i2.exec(e3 = n3[1] + e3) : null;
  }), order: 1, parse(e3, t3, r2) {
    const i3 = n2 ? +e3[2] : void 0, a2 = e3[0].replace(u, "\n").match(l2);
    let c2 = false;
    return { items: a2.map(function(e4, n3) {
      const i4 = o2.exec(e4)[0].length, l3 = new RegExp("^ {1," + i4 + "}", "gm"), s2 = e4.replace(l3, "").replace(o2, ""), d2 = n3 === a2.length - 1, u2 = -1 !== s2.indexOf("\n\n") || d2 && c2;
      c2 = u2;
      const p2 = r2.inline, f2 = r2.list;
      let h2;
      r2.list = true, u2 ? (r2.inline = false, h2 = ze(s2) + "\n\n") : (r2.inline = true, h2 = ze(s2));
      const m2 = t3(h2, r2);
      return r2.inline = p2, r2.list = f2, m2;
    }), ordered: n2, start: i3 };
  }, render: (t3, n3, i3) => e2(t3.ordered ? "ol" : "ul", { key: i3.key, start: t3.type === r.orderedList ? t3.start : void 0 }, t3.items.map(function(t4, r2) {
    return e2("li", { key: r2 }, n3(t4, i3));
  })) };
}
var $e = new RegExp(`^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['"]([\\s\\S]*?)['"])?\\s*\\)`);
var Se = /^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/;
var we = [p, y, k, z, A, L, H, be, ve];
var Ee = [...we, /^[^\n]+(?:  \n|\n{2,})/, O, B, R];
function ze(e2) {
  let t2 = e2.length;
  for (; t2 > 0 && e2[t2 - 1] <= " "; ) t2--;
  return e2.slice(0, t2);
}
function Le(e2) {
  return e2.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g, "a").replace(/[çÇ]/g, "c").replace(/[ðÐ]/g, "d").replace(/[ÈÉÊËéèêë]/g, "e").replace(/[ÏïÎîÍíÌì]/g, "i").replace(/[Ññ]/g, "n").replace(/[øØœŒÕõÔôÓóÒò]/g, "o").replace(/[ÜüÛûÚúÙù]/g, "u").replace(/[ŸÿÝý]/g, "y").replace(/[^a-z0-9- ]/gi, "").replace(/ /gi, "-").toLowerCase();
}
function Ae(e2) {
  return V.test(e2) ? "right" : q.test(e2) ? "center" : Q.test(e2) ? "left" : null;
}
function Oe(e2, t2, n2, r2) {
  const i2 = n2.inTable;
  n2.inTable = true;
  let l2 = [[]], o2 = "";
  function a2() {
    if (!o2) return;
    const e3 = l2[l2.length - 1];
    e3.push.apply(e3, t2(o2, n2)), o2 = "";
  }
  return e2.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach((e3, t3, n3) => {
    "|" === e3.trim() && (a2(), r2) ? 0 !== t3 && t3 !== n3.length - 1 && l2.push([]) : o2 += e3;
  }), a2(), n2.inTable = i2, l2;
}
function Te(e2, t2, n2) {
  n2.inline = true;
  const i2 = e2[2] ? e2[2].replace(Z, "").split("|").map(Ae) : [], l2 = e2[3] ? function(e3, t3, n3) {
    return e3.trim().split("\n").map(function(e4) {
      return Oe(e4, t3, n3, true);
    });
  }(e2[3], t2, n2) : [], o2 = Oe(e2[1], t2, n2, !!l2.length);
  return n2.inline = false, l2.length ? { align: i2, cells: l2, header: o2, type: r.table } : { children: o2, type: r.paragraph };
}
function Be(e2, t2) {
  return null == e2.align[t2] ? {} : { textAlign: e2.align[t2] };
}
function Me(e2) {
  return e2.inline = 1, e2;
}
function Re(e2) {
  return Me(function(t2, n2) {
    return n2.inline ? e2.exec(t2) : null;
  });
}
function Ie(e2) {
  return Me(function(t2, n2) {
    return n2.inline || n2.simple ? e2.exec(t2) : null;
  });
}
function De(e2) {
  return function(t2, n2) {
    return n2.inline || n2.simple ? null : e2.exec(t2);
  };
}
function Ue(e2) {
  return Me(function(t2) {
    return e2.exec(t2);
  });
}
function Ne(e2, t2) {
  if (t2.inline || t2.simple) return null;
  let n2 = "";
  e2.split("\n").every((e3) => (e3 += "\n", !we.some((t3) => t3.test(e3)) && (n2 += e3, !!e3.trim())));
  const r2 = ze(n2);
  return "" == r2 ? null : [n2, , r2];
}
var je = /(javascript|vbscript|data(?!:image)):/i;
function He(e2) {
  try {
    const t2 = decodeURIComponent(e2).replace(/[^A-Za-z0-9/:]/g, "");
    if (je.test(t2)) return null;
  } catch (e3) {
    return null;
  }
  return e2;
}
function Pe(e2) {
  return e2.replace(oe, "$1");
}
function _e(e2, t2, n2) {
  const r2 = n2.inline || false, i2 = n2.simple || false;
  n2.inline = true, n2.simple = true;
  const l2 = e2(t2, n2);
  return n2.inline = r2, n2.simple = i2, l2;
}
function Fe(e2, t2, n2) {
  const r2 = n2.inline || false, i2 = n2.simple || false;
  n2.inline = false, n2.simple = true;
  const l2 = e2(t2, n2);
  return n2.inline = r2, n2.simple = i2, l2;
}
function We(e2, t2, n2) {
  const r2 = n2.inline || false;
  n2.inline = false;
  const i2 = e2(t2, n2);
  return n2.inline = r2, i2;
}
var Ge = (e2, t2, n2) => ({ children: _e(t2, e2[2], n2) });
function Ze() {
  return {};
}
function qe() {
  return null;
}
function Qe(...e2) {
  return e2.filter(Boolean).join(" ");
}
function Ve(e2, t2, n2) {
  let r2 = e2;
  const i2 = t2.split(".");
  for (; i2.length && (r2 = r2[i2[0]], void 0 !== r2); ) i2.shift();
  return r2 || n2;
}
function Xe(n2 = "", i2 = {}) {
  function u2(e2, n3, ...r2) {
    const l2 = Ve(i2.overrides, `${e2}.props`, {});
    return i2.createElement(function(e3, t2) {
      const n4 = Ve(t2, e3);
      return n4 ? "function" == typeof n4 || "object" == typeof n4 && "render" in n4 ? n4 : Ve(t2, `${e3}.component`, e3) : e3;
    }(e2, i2.overrides), t({}, n3, l2, { className: Qe(null == n3 ? void 0 : n3.className, l2.className) || void 0 }), ...r2);
  }
  function Z2(e2) {
    e2 = e2.replace(w, "");
    let t2 = false;
    i2.forceInline ? t2 = true : i2.forceBlock || (t2 = false === W.test(e2));
    const n3 = ae2(oe2(t2 ? e2 : `${ze(e2).replace(ie, "")}

`, { inline: t2 }));
    for (; "string" == typeof n3[n3.length - 1] && !n3[n3.length - 1].trim(); ) n3.pop();
    if (null === i2.wrapper) return n3;
    const r2 = i2.wrapper || (t2 ? "span" : "div");
    let l2;
    if (n3.length > 1 || i2.forceWrapper) l2 = n3;
    else {
      if (1 === n3.length) return l2 = n3[0], "string" == typeof l2 ? u2("span", { key: "outer" }, l2) : l2;
      l2 = null;
    }
    return i2.createElement(r2, { key: "outer" }, l2);
  }
  function q2(e2, t2) {
    const n3 = t2.match(s);
    return n3 ? n3.reduce(function(t3, n4) {
      const r2 = n4.indexOf("=");
      if (-1 !== r2) {
        const o2 = function(e3) {
          return -1 !== e3.indexOf("-") && null === e3.match(M) && (e3 = e3.replace(j, function(e4, t4) {
            return t4.toUpperCase();
          })), e3;
        }(n4.slice(0, r2)).trim(), a2 = function(e3) {
          const t4 = e3[0];
          return ('"' === t4 || "'" === t4) && e3.length >= 2 && e3[e3.length - 1] === t4 ? e3.slice(1, -1) : e3;
        }(n4.slice(r2 + 1).trim()), s2 = l[o2] || o2;
        if ("ref" === s2) return t3;
        const d2 = t3[s2] = function(e3, t4, n5, r3) {
          return "style" === t4 ? function(e4) {
            const t5 = [];
            let n6 = "", r4 = false, i3 = false, l2 = "";
            if (!e4) return t5;
            for (let o4 = 0; o4 < e4.length; o4++) {
              const a3 = e4[o4];
              if ('"' !== a3 && "'" !== a3 || r4 || (i3 ? a3 === l2 && (i3 = false, l2 = "") : (i3 = true, l2 = a3)), "(" === a3 && n6.endsWith("url") ? r4 = true : ")" === a3 && r4 && (r4 = false), ";" !== a3 || i3 || r4) n6 += a3;
              else {
                const e5 = n6.trim();
                if (e5) {
                  const n7 = e5.indexOf(":");
                  if (n7 > 0) {
                    const r5 = e5.slice(0, n7).trim(), i4 = e5.slice(n7 + 1).trim();
                    t5.push([r5, i4]);
                  }
                }
                n6 = "";
              }
            }
            const o3 = n6.trim();
            if (o3) {
              const e5 = o3.indexOf(":");
              if (e5 > 0) {
                const n7 = o3.slice(0, e5).trim(), r5 = o3.slice(e5 + 1).trim();
                t5.push([n7, r5]);
              }
            }
            return t5;
          }(n5).reduce(function(t5, [n6, i3]) {
            return t5[n6.replace(/(-[a-z])/g, (e4) => e4[1].toUpperCase())] = r3(i3, e3, n6), t5;
          }, {}) : -1 !== c.indexOf(t4) ? r3(n5, e3, t4) : (n5.match(I) && (n5 = n5.slice(1, n5.length - 1)), "true" === n5 || "false" !== n5 && n5);
        }(e2, o2, a2, i2.sanitizer);
        "string" == typeof d2 && (O.test(d2) || R.test(d2)) && (t3[s2] = Z2(d2.trim()));
      } else "style" !== n4 && (t3[l[n4] || n4] = true);
      return t3;
    }, {}) : null;
  }
  i2.overrides = i2.overrides || {}, i2.sanitizer = i2.sanitizer || He, i2.slugify = i2.slugify || Le, i2.namedCodesToUnicode = i2.namedCodesToUnicode ? t({}, o, i2.namedCodesToUnicode) : o, i2.createElement = i2.createElement || e.createElement;
  const Q2 = [], V2 = {}, X2 = { [r.blockQuote]: { match: De(p), order: 1, parse(e2, t2, n3) {
    const [, r2, i3] = e2[0].replace(f, "").match(h);
    return { alert: r2, children: t2(i3, n3) };
  }, render(e2, t2, n3) {
    const l2 = { key: n3.key };
    return e2.alert && (l2.className = "markdown-alert-" + i2.slugify(e2.alert.toLowerCase(), Le), e2.children.unshift({ attrs: {}, children: [{ type: r.text, text: e2.alert }], noInnerParse: true, type: r.htmlBlock, tag: "header" })), u2("blockquote", l2, t2(e2.children, n3));
  } }, [r.breakLine]: { match: Ue(m), order: 1, parse: Ze, render: (e2, t2, n3) => u2("br", { key: n3.key }) }, [r.breakThematic]: { match: De(g), order: 1, parse: Ze, render: (e2, t2, n3) => u2("hr", { key: n3.key }) }, [r.codeBlock]: { match: De(k), order: 0, parse: (e2) => ({ lang: void 0, text: ze(e2[0].replace(/^ {4}/gm, "")).replace(ne, "$1") }), render: (e2, n3, r2) => u2("pre", { key: r2.key }, u2("code", t({}, e2.attrs, { className: e2.lang ? `lang-${e2.lang}` : "" }), e2.text)) }, [r.codeFenced]: { match: De(y), order: 0, parse: (e2) => ({ attrs: q2("code", e2[3] || ""), lang: e2[2] || void 0, text: e2[4], type: r.codeBlock }) }, [r.codeInline]: { match: Ie(x), order: 3, parse: (e2) => ({ text: e2[2].replace(ne, "$1") }), render: (e2, t2, n3) => u2("code", { key: n3.key }, e2.text) }, [r.footnote]: { match: De(C), order: 0, parse: (e2) => (Q2.push({ footnote: e2[2], identifier: e2[1] }), {}), render: qe }, [r.footnoteReference]: { match: Re($), order: 1, parse: (e2) => ({ target: `#${i2.slugify(e2[1], Le)}`, text: e2[1] }), render: (e2, t2, n3) => u2("a", { key: n3.key, href: i2.sanitizer(e2.target, "a", "href") }, u2("sup", { key: n3.key }, e2.text)) }, [r.gfmTask]: { match: Re(E), order: 1, parse: (e2) => ({ completed: "x" === e2[1].toLowerCase() }), render: (e2, t2, n3) => u2("input", { checked: e2.completed, key: n3.key, readOnly: true, type: "checkbox" }) }, [r.heading]: { match: De(i2.enforceAtxHeadings ? L : z), order: 1, parse: (e2, t2, n3) => ({ children: _e(t2, e2[2], n3), id: i2.slugify(e2[2], Le), level: e2[1].length }), render: (e2, t2, n3) => u2(`h${e2.level}`, { id: e2.id, key: n3.key }, t2(e2.children, n3)) }, [r.headingSetext]: { match: De(A), order: 0, parse: (e2, t2, n3) => ({ children: _e(t2, e2[1], n3), level: "=" === e2[2] ? 1 : 2, type: r.heading }) }, [r.htmlBlock]: { match: Ue(O), order: 1, parse(e2, t2, n3) {
    const [, r2] = e2[3].match(le), i3 = new RegExp(`^${r2}`, "gm"), l2 = e2[3].replace(i3, ""), o2 = (c2 = l2, Ee.some((e3) => e3.test(c2)) ? We : _e);
    var c2;
    const s2 = e2[1].toLowerCase(), d2 = -1 !== a.indexOf(s2), u3 = (d2 ? s2 : e2[1]).trim(), p2 = { attrs: q2(u3, e2[2]), noInnerParse: d2, tag: u3 };
    return n3.inAnchor = n3.inAnchor || "a" === s2, d2 ? p2.text = e2[3] : p2.children = o2(t2, l2, n3), n3.inAnchor = false, p2;
  }, render: (e2, n3, r2) => u2(e2.tag, t({ key: r2.key }, e2.attrs), e2.text || (e2.children ? n3(e2.children, r2) : "")) }, [r.htmlSelfClosing]: { match: Ue(R), order: 1, parse(e2) {
    const t2 = e2[1].trim();
    return { attrs: q2(t2, e2[2] || ""), tag: t2 };
  }, render: (e2, n3, r2) => u2(e2.tag, t({}, e2.attrs, { key: r2.key })) }, [r.htmlComment]: { match: Ue(B), order: 1, parse: () => ({}), render: qe }, [r.image]: { match: Ie(Se), order: 1, parse: (e2) => ({ alt: e2[1], target: Pe(e2[2]), title: e2[3] }), render: (e2, t2, n3) => u2("img", { key: n3.key, alt: e2.alt || void 0, title: e2.title || void 0, src: i2.sanitizer(e2.target, "img", "src") }) }, [r.link]: { match: Re($e), order: 3, parse: (e2, t2, n3) => ({ children: Fe(t2, e2[1], n3), target: Pe(e2[2]), title: e2[3] }), render: (e2, t2, n3) => u2("a", { key: n3.key, href: i2.sanitizer(e2.target, "a", "href"), title: e2.title }, t2(e2.children, n3)) }, [r.linkAngleBraceStyleDetector]: { match: Re(N), order: 0, parse: (e2) => ({ children: [{ text: e2[1], type: r.text }], target: e2[1], type: r.link }) }, [r.linkBareUrlDetector]: { match: Me((e2, t2) => t2.inAnchor || i2.disableAutoLink ? null : Re(D)(e2, t2)), order: 0, parse: (e2) => ({ children: [{ text: e2[1], type: r.text }], target: e2[1], title: void 0, type: r.link }) }, [r.linkMailtoDetector]: { match: Re(U), order: 0, parse(e2) {
    let t2 = e2[1], n3 = e2[1];
    return d.test(n3) || (n3 = "mailto:" + n3), { children: [{ text: t2.replace("mailto:", ""), type: r.text }], target: n3, type: r.link };
  } }, [r.orderedList]: Ce(u2, 1), [r.unorderedList]: Ce(u2, 2), [r.newlineCoalescer]: { match: De(b), order: 3, parse: Ze, render: () => "\n" }, [r.paragraph]: { match: Me(Ne), order: 3, parse: Ge, render: (e2, t2, n3) => u2("p", { key: n3.key }, t2(e2.children, n3)) }, [r.ref]: { match: Re(P), order: 0, parse: (e2) => (V2[e2[1]] = { target: e2[2], title: e2[4] }, {}), render: qe }, [r.refImage]: { match: Ie(_), order: 0, parse: (e2) => ({ alt: e2[1] || void 0, ref: e2[2] }), render: (e2, t2, n3) => V2[e2.ref] ? u2("img", { key: n3.key, alt: e2.alt, src: i2.sanitizer(V2[e2.ref].target, "img", "src"), title: V2[e2.ref].title }) : null }, [r.refLink]: { match: Re(F), order: 0, parse: (e2, t2, n3) => ({ children: t2(e2[1], n3), fallbackChildren: e2[0], ref: e2[2] }), render: (e2, t2, n3) => V2[e2.ref] ? u2("a", { key: n3.key, href: i2.sanitizer(V2[e2.ref].target, "a", "href"), title: V2[e2.ref].title }, t2(e2.children, n3)) : u2("span", { key: n3.key }, e2.fallbackChildren) }, [r.table]: { match: De(H), order: 1, parse: Te, render(e2, t2, n3) {
    const r2 = e2;
    return u2("table", { key: n3.key }, u2("thead", null, u2("tr", null, r2.header.map(function(e3, i3) {
      return u2("th", { key: i3, style: Be(r2, i3) }, t2(e3, n3));
    }))), u2("tbody", null, r2.cells.map(function(e3, i3) {
      return u2("tr", { key: i3 }, e3.map(function(e4, i4) {
        return u2("td", { key: i4, style: Be(r2, i4) }, t2(e4, n3));
      }));
    })));
  } }, [r.text]: { match: Ue(re), order: 4, parse: (e2) => ({ text: e2[0].replace(T, (e3, t2) => i2.namedCodesToUnicode[t2] ? i2.namedCodesToUnicode[t2] : e3) }), render: (e2) => e2.text }, [r.textBolded]: { match: Ie(J), order: 2, parse: (e2, t2, n3) => ({ children: t2(e2[2], n3) }), render: (e2, t2, n3) => u2("strong", { key: n3.key }, t2(e2.children, n3)) }, [r.textEmphasized]: { match: Ie(K), order: 3, parse: (e2, t2, n3) => ({ children: t2(e2[2], n3) }), render: (e2, t2, n3) => u2("em", { key: n3.key }, t2(e2.children, n3)) }, [r.textEscaped]: { match: Ie(te), order: 1, parse: (e2) => ({ text: e2[1], type: r.text }) }, [r.textMarked]: { match: Ie(Y), order: 3, parse: Ge, render: (e2, t2, n3) => u2("mark", { key: n3.key }, t2(e2.children, n3)) }, [r.textStrikethroughed]: { match: Ie(ee), order: 3, parse: Ge, render: (e2, t2, n3) => u2("del", { key: n3.key }, t2(e2.children, n3)) } };
  true === i2.disableParsingRawHTML && (delete X2[r.htmlBlock], delete X2[r.htmlSelfClosing]);
  const oe2 = function(e2) {
    let t2 = Object.keys(e2);
    function n3(r2, i3) {
      let l2, o2, a2 = [], c2 = "", s2 = "";
      for (i3.prevCapture = i3.prevCapture || ""; r2; ) {
        let d2 = 0;
        for (; d2 < t2.length; ) {
          if (c2 = t2[d2], l2 = e2[c2], i3.inline && !l2.match.inline) {
            d2++;
            continue;
          }
          const u3 = l2.match(r2, i3);
          if (u3) {
            s2 = u3[0], i3.prevCapture += s2, r2 = r2.substring(s2.length), o2 = l2.parse(u3, n3, i3), null == o2.type && (o2.type = c2), a2.push(o2);
            break;
          }
          d2++;
        }
      }
      return i3.prevCapture = "", a2;
    }
    return t2.sort(function(t3, n4) {
      let r2 = e2[t3].order, i3 = e2[n4].order;
      return r2 !== i3 ? r2 - i3 : t3 < n4 ? -1 : 1;
    }), function(e3, t3) {
      return n3(function(e4) {
        return e4.replace(v, "\n").replace(S, "").replace(G, "    ");
      }(e3), t3);
    };
  }(X2), ae2 = (ce2 = /* @__PURE__ */ function(e2, t2) {
    return function(n3, r2, i3) {
      const l2 = e2[n3.type].render;
      return t2 ? t2(() => l2(n3, r2, i3), n3, r2, i3) : l2(n3, r2, i3);
    };
  }(X2, i2.renderRule), function e2(t2, n3 = {}) {
    if (Array.isArray(t2)) {
      const r2 = n3.key, i3 = [];
      let l2 = false;
      for (let r3 = 0; r3 < t2.length; r3++) {
        n3.key = r3;
        const o2 = e2(t2[r3], n3), a2 = "string" == typeof o2;
        a2 && l2 ? i3[i3.length - 1] += o2 : null !== o2 && i3.push(o2), l2 = a2;
      }
      return n3.key = r2, i3;
    }
    return ce2(t2, e2, n3);
  });
  var ce2;
  const se2 = Z2(n2);
  return Q2.length ? u2("div", null, se2, u2("footer", { key: "footer" }, Q2.map(function(e2) {
    return u2("div", { id: i2.slugify(e2.identifier, Le), key: e2.identifier }, e2.identifier, ae2(oe2(e2.footnote, { inline: true })));
  }))) : se2;
}
var index_modern_default = (t2) => {
  let { children: r2 = "", options: i2 } = t2, l2 = function(e2, t3) {
    if (null == e2) return {};
    var n2, r3, i3 = {}, l3 = Object.keys(e2);
    for (r3 = 0; r3 < l3.length; r3++) t3.indexOf(n2 = l3[r3]) >= 0 || (i3[n2] = e2[n2]);
    return i3;
  }(t2, n);
  return e.cloneElement(Xe(r2, i2), l2);
};
export {
  r as RuleType,
  Xe as compiler,
  index_modern_default as default,
  He as sanitizer,
  Le as slugify
};
//# sourceMappingURL=markdown-to-jsx.js.map
