import "dotenv/config";
import http from "http";
import app from "./app.js";
import { Server } from "socket.io";
import jwt from "jsonwebtoken";
import mongoose from "mongoose";
import projectModel from "./models/project.model.js";
import {generateResult} from "./services/ai.service.js";

const port = process.env.PORT || 3000;

const server = http.createServer(app);

const io = new Server(server,{
    cors: {
        origin: "http://localhost:5173",
        methods: ["GET", "POST"],
        allowedHeaders: ["my-custom-header"],
        credentials: true
    }
});

io.use(async(socket, next) => {
    try {
        const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization?.split(" ")[1];
        const projectId = socket.handshake.query.projectId;

        if(!mongoose.Types.ObjectId.isValid(projectId)){
            throw new Error("Invalid projectId");
        }

        socket.project = await projectModel.findById(projectId);

        if(!socket.project){
            throw new Error("Project not found");
        }

        if(!token){
            throw new Error("Unauthorized");
        }
        
        const decoded = jwt.verify(token,process.env.JWT_SECRET);
        if(!decoded){
            throw new Error("Unauthorized");
        }
        socket.user = decoded;
        next();
    } catch (error) {
        next(error)
    }
});


io.on('connection', socket => {
    socket.roomId = socket.project._id.toString();
    
    console.log("New client connected");
    socket.join(socket.roomId);

    socket.on('project-message', async(message) => {
        const data = message.text;
        const aiIsIncludedInMessage = data.includes("@ai");
        if(aiIsIncludedInMessage){
            const prompt = data.replace("@ai","");
            const aiResponse = await generateResult(prompt);
            const aiMessage = {
                id: generateUniqueId(),
                text: aiResponse,
                sender: "AI",
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            };
            io.to(socket.roomId).emit('project-message', aiMessage);
        }
        io.to(socket.roomId).emit('project-message', message);
    });

    socket.on('disconnect', () => { 
        console.log("Client disconnected");
        socket.leave(socket.roomId);
    });
});

server.listen(port,()=>{
    console.log(`Server is running on port ${port}`);
});
 