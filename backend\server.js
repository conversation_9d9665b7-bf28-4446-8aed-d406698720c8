import "dotenv/config";
import http from "http";
import app from "./app.js";
import { Server } from "socket.io";
import jwt from "jsonwebtoken";
import mongoose from "mongoose";
import projectModel from "./models/project.model.js";
import {generateResult} from "./services/ai.service.js";

// Helper function to generate unique IDs
const generateUniqueId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

const port = process.env.PORT || 3000;

const server = http.createServer(app);

const io = new Server(server,{
    cors: {
        origin: "http://localhost:5173",
        methods: ["GET", "POST"],
        allowedHeaders: ["my-custom-header"],
        credentials: true
    }
});

io.use(async(socket, next) => {
    try {
        const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization?.split(" ")[1];
        const projectId = socket.handshake.query.projectId;

        if(!mongoose.Types.ObjectId.isValid(projectId)){
            throw new Error("Invalid projectId");
        }

        socket.project = await projectModel.findById(projectId);

        if(!socket.project){
            throw new Error("Project not found");
        }

        if(!token){
            throw new Error("Unauthorized");
        }
        
        const decoded = jwt.verify(token,process.env.JWT_SECRET);
        if(!decoded){
            throw new Error("Unauthorized");
        }
        socket.user = decoded;
        next();
    } catch (error) {
        next(error)
    }
});


io.on('connection', socket => {
    socket.roomId = socket.project._id.toString();
    
    console.log("New client connected");
    socket.join(socket.roomId);

    socket.on('project-message', async(message) => {
        try {
            console.log('Received message:', message);
            const data = message.text;
            const aiIsIncludedInMessage = data.includes("@ai");

            if(aiIsIncludedInMessage){
                console.log('AI message detected, generating response...');
                const prompt = data.replace("@ai","").trim();
                console.log('Prompt for AI:', prompt);

                const aiResponse = await generateResult(prompt);
                console.log('AI Response:', aiResponse);

                const aiMessage = {
                    id: generateUniqueId(),
                    text: aiResponse,
                    sender: "AI",
                    timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                };

                console.log('Sending AI message:', aiMessage);
                io.to(socket.roomId).emit('project-message', aiMessage);
            }

            // Always emit the original message
            io.to(socket.roomId).emit('project-message', message);
        } catch (error) {
            console.error('Error handling project message:', error);

            // Send error message to client
            const errorMessage = {
                id: generateUniqueId(),
                text: "Sorry, I encountered an error while processing your request.",
                sender: "AI",
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            };
            io.to(socket.roomId).emit('project-message', errorMessage);
        }
    });

    socket.on('disconnect', () => { 
        console.log("Client disconnected");
        socket.leave(socket.roomId);
    });
});

server.listen(port,()=>{
    console.log(`Server is running on port ${port}`);
});
 